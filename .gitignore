.buildpath
.hgignore.swp
.project
.orig
.swp
.idea/
.settings/
.vscode/
bin
**/.DS_Store
gf
main
main.exe
output/
manifest/output/
temp/
temp.yaml
bin
*.log
main *
doc
.roomodes
build/message-processor
build/bot-gateway
test_build
*example.go
financial-ops-module
backup
build/app
vendor
# Added by Task Master AI
# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.DS_Store
.roo
.cursor
docs/game
docs/img
docs/dev
# Task files
tasks.json
tasks/ 
.claude
docs/admin/images
telegram-bot-api
