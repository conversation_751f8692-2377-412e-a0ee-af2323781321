package admin

import (
	"context"
	"fmt"
	"strings"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/frame/g"

	"telegram-bot-api/internal/model"
	"telegram-bot-api/internal/model/callback"
	"telegram-bot-api/internal/service"
	"telegram-bot-api/internal/tenant"
)

// DateInputState stores temporary date input state
type DateInputState struct {
	UserID     int64
	StartDate  string
	EndDate    string
	WaitingFor string // "date_range", ""
}

var dateInputStates = make(map[int64]*DateInputState)

// handleDailyReports handles the daily deposit/withdrawal reports
func handleDailyReports(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	// Verify admin permission
	isAdmin, err := service.Admin().IsCurrentUserAdmin(ctx)
	if err != nil {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionError")), nil
	}
	if !isAdmin {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminPermissionDenied")), nil
	}

	// Set user state to wait for date range input using the UserState system
	userID := callbackQuery.From.ID
	userState := model.NewUserState(
		model.StateAwaitingDailyReportDateRange,
		make(map[string]string), // Empty context for now
		model.InputTypeText,
		3,   // Max 3 retries
		300, // 5 minutes TTL
	)

	err = service.UserState().SetUserStateByTelegramId(ctx, userID, userState)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to set user state for daily report: %v", err)
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminSystemError")), nil
	}

	// Build message for date range input
	text := BuildDailyReportMenuMessage(ctx, nil)
	keyboard := BuildDailyReportKeyboard(ctx, nil)

	return &callback.EditMessageResponse{
		CallbackQueryID: callbackQuery.ID,
		ChatID:          callbackQuery.Message.Chat.ID,
		MessageID:       callbackQuery.Message.MessageID,
		Text:            text,
		ParseMode:       "HTML",
		InlineKeyboard:  &keyboard,
	}, nil
}

// handleDailyReportPage handles pagination for daily reports
func handleDailyReportPage(ctx context.Context, callbackQuery *tgbotapi.CallbackQuery, params string) (callback.CallbackResponse, error) {
	// Parse params: page:startDate:endDate
	parts := strings.Split(params, ":")
	if len(parts) < 3 {
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminSystemError")), nil
	}

	page := 1
	if parts[0] != "" {
		fmt.Sscanf(parts[0], "%d", &page)
	}

	startDateStr := parts[1]
	endDateStr := parts[2]

	// Validate date range
	startDate, endDate, err := service.DailyReportService().ValidateDateRange(startDateStr, endDateStr)
	if err != nil {
		return callback.NewAnswerCallback(callbackQuery.ID, err.Error()), nil
	}

	// Generate report with pagination (5 items per page)
	report, err := service.DailyReportService().GetDailyDepositWithdrawalReport(ctx, startDate, endDate, page, 5)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to generate daily report page %d: %v", page, err)
		return callback.NewAnswerCallback(callbackQuery.ID, service.I18n().T(ctx, "AdminDailyReportError")), nil
	}

	// Format report
	text := BuildDailyReportResultMessage(ctx, report)

	// Restore state for keyboard
	userID := callbackQuery.From.ID
	state, exists := dateInputStates[userID]
	if !exists {
		state = &DateInputState{
			UserID:    userID,
			StartDate: startDateStr,
			EndDate:   endDateStr,
		}
		dateInputStates[userID] = state
	}

	keyboard := BuildDailyReportResultKeyboard(ctx, report, state)

	return &callback.EditMessageResponse{
		CallbackQueryID: callbackQuery.ID,
		ChatID:          callbackQuery.Message.Chat.ID,
		MessageID:       callbackQuery.Message.MessageID,
		Text:            text,
		ParseMode:       "HTML",
		InlineKeyboard:  &keyboard,
	}, nil
}

// handleDailyReportTextInput processes text messages for date input
func handleDailyReportTextInput(ctx context.Context, message *tgbotapi.Message) error {
	userID := message.From.ID

	// Check if user is in the correct state using UserState system
	userState, err := service.UserState().GetUserStateByTelegramId(ctx, userID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get user state for daily report input: %v", err)
		return nil // Don't handle if we can't get state
	}
	if userState == nil || userState.State != model.StateAwaitingDailyReportDateRange {
		return nil // Not waiting for date range input
	}

	dateInput := strings.TrimSpace(message.Text)
	bot, err := getDailyReportTenantBot(ctx)
	if err != nil {
		return err
	}

	// Parse the date range (expecting two dates separated by space)
	parts := strings.Fields(dateInput)
	if len(parts) != 2 {
		// Increment retry count
		userState.RetryCount++
		if userState.RetryCount >= userState.MaxRetries {
			// Clear state and send final error
			service.UserState().ClearUserStateByTelegramId(ctx, userID)
			errorMsg := tgbotapi.NewMessage(message.Chat.ID, "❌ 输入错误次数过多，请重新开始")
			bot.Send(errorMsg)
			return nil
		}

		// Update state with incremented retry count
		service.UserState().SetUserStateByTelegramId(ctx, userID, userState)

		// Send error message
		errorMsg := tgbotapi.NewMessage(message.Chat.ID, fmt.Sprintf("❌ 格式错误：请输入两个日期，用空格分隔\n剩余尝试次数：%d", userState.MaxRetries-userState.RetryCount))
		errorMsg.ReplyToMessageID = message.MessageID
		bot.Send(errorMsg)
		return nil
	}

	startDateStr := parts[0]
	endDateStr := parts[1]

	// Validate date format for both dates
	if !isValidDateFormat(startDateStr) || !isValidDateFormat(endDateStr) {
		// Send error message
		errorMsg := tgbotapi.NewMessage(message.Chat.ID, "❌ 格式错误：日期格式应为 YYYY-MM-DD")
		errorMsg.ReplyToMessageID = message.MessageID
		bot.Send(errorMsg)
		return nil
	}

	// Validate date range using service
	startDate, endDate, err := service.DailyReportService().ValidateDateRange(startDateStr, endDateStr)
	if err != nil {
		errorText := "❌ "
		// Translate common error messages to Chinese
		errMsg := err.Error()
		switch {
		case strings.Contains(errMsg, "start date cannot be after end date"):
			errorText += "开始日期需小于结束日期"
		case strings.Contains(errMsg, "invalid start date format"):
			errorText += "开始日期格式错误，请使用 YYYY-MM-DD"
		case strings.Contains(errMsg, "invalid end date format"):
			errorText += "结束日期格式错误，请使用 YYYY-MM-DD"
		case strings.Contains(errMsg, "start date cannot be in the future"):
			errorText += "开始日期不能是未来日期"
		case strings.Contains(errMsg, "start date cannot be before 2020-01-01"):
			errorText += "开始日期不能早于 2020-01-01"
		default:
			errorText += "日期范围无效"
		}
		errorMsg := tgbotapi.NewMessage(message.Chat.ID, errorText)
		errorMsg.ReplyToMessageID = message.MessageID
		bot.Send(errorMsg)
		return nil
	}

	// Clear user state since we successfully processed the input
	err = service.UserState().ClearUserStateByTelegramId(ctx, userID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to clear user state after successful daily report input: %v", err)
	}

	// Generate report with 5 items per page
	report, err := service.DailyReportService().GetDailyDepositWithdrawalReport(ctx, startDate, endDate, 1, 5)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to generate daily report: %v", err)
		errorMsg := tgbotapi.NewMessage(message.Chat.ID, "❌ 生成报表失败")
		errorMsg.ReplyToMessageID = message.MessageID
		bot.Send(errorMsg)
		return nil
	}

	// Format report
	text := BuildDailyReportResultMessage(ctx, report)
	// Create a temporary state for keyboard building
	tempState := &DateInputState{
		UserID:    userID,
		StartDate: startDateStr,
		EndDate:   endDateStr,
	}
	keyboard := BuildDailyReportResultKeyboard(ctx, report, tempState)

	msg := tgbotapi.NewMessage(message.Chat.ID, text)
	msg.ParseMode = "HTML"
	msg.ReplyMarkup = keyboard

	bot.Send(msg)
	return nil
}

// isValidDateFormat checks if the date string matches YYYY-MM-DD format
func isValidDateFormat(dateStr string) bool {
	if len(dateStr) != 10 {
		return false
	}

	// Check format: YYYY-MM-DD
	if dateStr[4] != '-' || dateStr[7] != '-' {
		return false
	}

	// Check if all parts are numeric
	yearStr := dateStr[0:4]
	monthStr := dateStr[5:7]
	dayStr := dateStr[8:10]

	for _, r := range yearStr + monthStr + dayStr {
		if r < '0' || r > '9' {
			return false
		}
	}

	return true
}

// BuildDailyReportMenuMessage builds the daily report menu message
func BuildDailyReportMenuMessage(ctx context.Context, _ *DateInputState) string {
	header := fmt.Sprintf("📊 <b>%s</b>\n\n", service.I18n().T(ctx, "AdminDailyReportHeader"))

	msg := header
	msg += service.I18n().T(ctx, "AdminDailyReportDescription") + "\n\n"

	// Show instructions for date range input
	msg += "📝 请回复要查询的日期范围，使用空格分隔，例如：2025-01-01 2025-05-30"

	return msg
}

// BuildDailyReportKeyboard builds the keyboard for daily report menu
func BuildDailyReportKeyboard(ctx context.Context, _ *DateInputState) tgbotapi.InlineKeyboardMarkup {
	var rows [][]tgbotapi.InlineKeyboardButton

	// Back button only
	backBtn := tgbotapi.NewInlineKeyboardButtonData(
		"🔙 "+service.I18n().T(ctx, "AdminButtonBackToAdmin"),
		"admin_center",
	)
	rows = append(rows, []tgbotapi.InlineKeyboardButton{backBtn})

	return tgbotapi.NewInlineKeyboardMarkup(rows...)
}

// BuildDailyReportResultMessage builds the report result message
func BuildDailyReportResultMessage(_ context.Context, report *service.DailyReportResult) string {
	// Handle nil dates gracefully
	startDateStr := "未知"
	endDateStr := "未知"

	if report.StartDate != nil {
		startDateStr = report.StartDate.Format("Y-m-d")
	}
	if report.EndDate != nil {
		endDateStr = report.EndDate.Format("Y-m-d")
	}

	msg := fmt.Sprintf("日期范围：%s ~ %s\n", startDateStr, endDateStr)

	// Format the table
	tableData := service.DailyReportService().FormatReportTable(report)
	msg += fmt.Sprintf("<pre>%s</pre>", tableData)

	// Add pagination info at the bottom if there are multiple pages
	if report.TotalCount > int64(report.PageSize) {
		totalPages := int((report.TotalCount + int64(report.PageSize) - 1) / int64(report.PageSize))
		msg += fmt.Sprintf("\n\n📄 第 %d/%d 页 (共 %d 条记录)", 
			report.Page, totalPages, report.TotalCount)
	}

	return msg
}

// BuildDailyReportResultKeyboard builds keyboard for report results
func BuildDailyReportResultKeyboard(ctx context.Context, report *service.DailyReportResult, state *DateInputState) tgbotapi.InlineKeyboardMarkup {
	var rows [][]tgbotapi.InlineKeyboardButton

	// Pagination (without the page info button)
	if report.TotalCount > int64(report.PageSize) {
		var paginationButtons []tgbotapi.InlineKeyboardButton

		totalPages := int((report.TotalCount + int64(report.PageSize) - 1) / int64(report.PageSize))

		// Previous button
		if report.Page > 1 {
			prevBtn := tgbotapi.NewInlineKeyboardButtonData(
				"⬅️ "+service.I18n().T(ctx, "AdminButtonPrevious"),
				fmt.Sprintf("admin_daily_report_page:%d:%s:%s", report.Page-1, state.StartDate, state.EndDate),
			)
			paginationButtons = append(paginationButtons, prevBtn)
		}

		// Next button
		if report.Page < totalPages {
			nextBtn := tgbotapi.NewInlineKeyboardButtonData(
				service.I18n().T(ctx, "AdminButtonNext")+" ➡️",
				fmt.Sprintf("admin_daily_report_page:%d:%s:%s", report.Page+1, state.StartDate, state.EndDate),
			)
			paginationButtons = append(paginationButtons, nextBtn)
		}

		// Only add pagination row if there are buttons
		if len(paginationButtons) > 0 {
			rows = append(rows, paginationButtons)
		}
	}

	// Export button (future enhancement)
	// exportBtn := tgbotapi.NewInlineKeyboardButtonData(
	// 	"📥 "+service.I18n().T(ctx, "AdminButtonExport"),
	// 	"admin_daily_report_export",
	// )
	// rows = append(rows, []tgbotapi.InlineKeyboardButton{exportBtn})

	// Back to date selection
	backBtn := tgbotapi.NewInlineKeyboardButtonData(
		"🔙 "+service.I18n().T(ctx, "AdminButtonBackToDateSelection"),
		"admin_daily_reports",
	)
	rows = append(rows, []tgbotapi.InlineKeyboardButton{backBtn})

	return tgbotapi.NewInlineKeyboardMarkup(rows...)
}

// getDailyReportTenantBot gets the bot instance for the current tenant
func getDailyReportTenantBot(ctx context.Context) (*tgbotapi.BotAPI, error) {
	tenantId, ok := tenant.GetTenantIdFromContext(ctx)
	if !ok {
		return nil, fmt.Errorf("tenantId not found in context")
	}
	return service.TenantBotManager().GetBot(ctx, tenantId)
}
