package redpacket

import (
	"context"
	"strconv"
	"strings"
	cover "telegram-bot-api/internal/bot/redpacket/cover"
	localConsts "telegram-bot-api/internal/consts"
	"telegram-bot-api/internal/model/callback"
	"telegram-bot-api/internal/service"

	"github.com/a19ba14d/tg-bot-common/consts"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// HandleRedPacketSubmenuCallback handles callbacks originating from the red packet submenu and subsequent steps.
func HandleRedPacketSubmenuCallback(ctx context.Context, cq *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	// Check for inline query callbacks (from shared red packets)
	if cq.Message == nil || cq.Message.Chat == nil {
		// This might be an inline query callback (e.g., from shared red packets)
		// Handle special inline callbacks that don't require message context
		if strings.HasPrefix(cq.Data, "rp_claim:") || strings.HasPrefix(cq.Data, "rp_verify:") {
			g.Log().Infof(ctx, "HandleRedPacketSubmenuCallback: Handling inline callback: %s", cq.Data)
			// Continue processing without message context
		} else {
			g.Log().Warning(ctx, "HandleRedPacketSubmenuCallback: Received callback query with nil Message or Chat for non-inline callback")
			return nil, nil // Or return an error response
		}
	}

	callbackData := cq.Data
	i18n := service.I18n().Instance()

	// Route based on callback data prefix or specific value
	switch {
	// --- Close Claim List Handler ---
	case callbackData == "rp:close_claim_list":
		return handleCloseClaimList(ctx, cq)

	// --- Empty Red Packet Handler ---
	case strings.HasPrefix(callbackData, "rp:empty:"):
		// Extract UUID (not used currently but available if needed)
		// uuid := strings.TrimPrefix(callbackData, "rp:empty:")

		// Show alert message that red packet is fully claimed
		alertText := i18n.T(ctx, "{#RedPacketFullyClaimed}")
		return callback.NewAlertResponse(alertText), nil

	// --- Canceled Red Packet Handler ---
	case strings.HasPrefix(callbackData, "rp:canceled:"):
		// Extract UUID (not used currently but available if needed)
		// uuid := strings.TrimPrefix(callbackData, "rp:canceled:")

		// Show alert message that red packet is canceled
		alertText := i18n.T(ctx, "{#RedPacketCancelledMessage}")
		return callback.NewAlertResponse(alertText), nil

	// --- Expired Red Packet Handler ---
	case strings.HasPrefix(callbackData, "rp:expired:"):
		// Extract UUID (not used currently but available if needed)
		// uuid := strings.TrimPrefix(callbackData, "rp:expired:")

		// Show alert message that red packet is expired
		alertText := i18n.T(ctx, "{#RedPacketExpiredMessage}")
		return callback.NewAlertResponse(alertText), nil

	// --- Red Packet Set Cover (New Logic) ---
	case callbackData == consts.CallbackRedPacketSetCover:
		// Check if this is called from red packet creation flow or cover management
		// If from red packet creation, show cover selection for red packet
		// If from cover management, show cover management page

		// Get user state to determine context
		userState, err := service.UserState().GetUserStateByTelegramId(ctx, cq.From.ID)
		if err == nil && userState != nil && userState.State == localConsts.UserStateWaitingRedPacketPaymentConfirm {
			// User is in red packet creation flow - show cover selection for red packet
			return cover.HandleSelectCoverForRedPacket(ctx, cq, 1)
		} else {
			// Default behavior - show cover management page
			return cover.HandleSetCover(ctx, cq, 1)
		}

	case strings.HasPrefix(callbackData, "rp:set_cover:page:"):
		// Handle pagination for setting cover using the new handler
		pageStr := strings.TrimPrefix(callbackData, "rp:set_cover:page:")
		page := gconv.Int(pageStr)
		if page < 1 {
			page = 1 // Ensure page is at least 1
		}
		return cover.HandleSetCover(ctx, cq, page)

	// --- Red Packet Cover Selection for Red Packet Creation ---
	case strings.HasPrefix(callbackData, "rp:select_cover:page:"):
		// Handle pagination for cover selection during red packet creation
		pageStr := strings.TrimPrefix(callbackData, "rp:select_cover:page:")
		page := gconv.Int(pageStr)
		if page < 1 {
			page = 1
		}
		return cover.HandleSelectCoverForRedPacket(ctx, cq, page)

	case callbackData == "rp:select_cover:confirm":
		// Handle cover selection confirmation during red packet creation
		return cover.HandleConfirmCoverSelection(ctx, cq)

	case callbackData == localConsts.CallbackRedPacketBackFromCover:
		// Handle back to payment confirmation page from cover selection
		return handleBackToPaymentConfirmation(ctx, cq)

	// --- New Cover Delete Confirmation/Execution Handlers ---
	case strings.HasPrefix(callbackData, "rp:cover_del_confirm:"):
		payload := strings.TrimPrefix(callbackData, "rp:cover_del_confirm:")
		parts := strings.Split(payload, ":")
		// Expecting format: <imageID>:<page>
		if len(parts) != 2 {
			g.Log().Errorf(ctx, "HandleRedPacketSubmenuCallback: Invalid format for rp:cover_del_confirm (expected 2 parts): %s", callbackData)
			return callback.NewAlertResponse("无效操作"), nil
		}
		imageID := gconv.Int64(parts[0])
		page := gconv.Int(parts[1])
		if page < 1 {
			page = 1
		}
		// Call the new handler (fileID is retrieved from UserState inside the handler)
		return cover.HandleDeleteConfirmation(ctx, cq, imageID, page)

	case strings.HasPrefix(callbackData, "rp:cover_del_exec:"):
		payload := strings.TrimPrefix(callbackData, "rp:cover_del_exec:")
		parts := strings.Split(payload, ":")
		if len(parts) != 2 {
			g.Log().Errorf(ctx, "HandleRedPacketSubmenuCallback: Invalid format for rp:cover_del_exec: %s", callbackData)
			return callback.NewAlertResponse("无效操作"), nil // Or a more specific error
		}
		imageID := gconv.Int64(parts[0])
		page := gconv.Int(parts[1]) // Keep page info for potential refresh logic after delete
		if page < 1 {
			page = 1
		}
		// Call the new handler (to be created)
		return cover.HandleDeleteExecute(ctx, cq, imageID, page)

	// --- Existing Cover Handlers (Keep for Upload Prompt, Comment out old delete) ---
	// Note: CallbackRedPacketCoverPagePrefix ("rp:cover_page_") seems redundant now,
	// but keep it if it's used elsewhere or for a different cover context.
	// If it was intended for the same purpose, it should be removed or refactored.
	case strings.HasPrefix(callbackData, consts.CallbackRedPacketCoverPagePrefix):
		g.Log().Warningf(ctx, "HandleRedPacketSubmenuCallback: Potentially redundant prefix %s used: %s", consts.CallbackRedPacketCoverPagePrefix, callbackData)
		pageStr := strings.TrimPrefix(callbackData, consts.CallbackRedPacketCoverPagePrefix)
		page := gconv.Int(pageStr)
		if page < 1 {
			page = 1
		}
		// Assuming handleShowCoverPage is the old/placeholder logic, maybe redirect?
		// For now, keep calling the old placeholder if this prefix is distinct.
		return handleShowCoverPage(ctx, cq, page) // Keep calling old placeholder for this specific prefix

	// case strings.HasPrefix(callbackData, consts.CallbackRedPacketCoverDelConfirmPrefix): // Commented out old handler
	// 	payload := strings.TrimPrefix(callbackData, consts.CallbackRedPacketCoverDelConfirmPrefix)
	// 	// Assuming the delete logic expects format like "imageId&page=N"
	// 	parts := strings.Split(payload, "&page=")
	// 	imageIdStr := parts[0]
	// 	imageId := gconv.Int64(imageIdStr)
	// 	currentPage := 1
	// 	if len(parts) == 2 {
	// 		currentPage = gconv.Int(parts[1])
	// 		if currentPage < 1 {
	// 			currentPage = 1
	// 		}
	// 	} else {
	// 		g.Log().Warningf(ctx, "HandleRedPacketSubmenuCallback: Could not parse page from delete confirm: %s", callbackData)
	// 		// imageId is already parsed from imageIdStr
	// 	}
	// 	// Assuming handleCoverDeleteConfirm exists and handles this
	// 	return handleCoverDeleteConfirm(ctx, cq, imageId, currentPage) // Pass currentPage
	//
	// case strings.HasPrefix(callbackData, consts.CallbackRedPacketCoverDelExecPrefix): // Commented out old handler
	// 	// Assuming delete exec logic exists
	// 	imageIdStr := strings.TrimPrefix(callbackData, consts.CallbackRedPacketCoverDelExecPrefix)
	// 	imageId := gconv.Int64(imageIdStr)
	// 	return handleCoverDeleteExec(ctx, cq, imageId)

	case callbackData == consts.CallbackRedPacketCoverUploadPrompt:
		// Call the new handler for upload prompt
		return cover.HandleUploadPrompt(ctx, cq)

	// --- New Red Packet Creation Flow Handlers ---
	case callbackData == consts.CallbackRedPacketAdd:
		return handleRedPacketAdd(ctx, cq)
	case strings.HasPrefix(callbackData, consts.CallbackSelectRedPacketTokenPrefix):
		return handleSelectToken(ctx, cq, callbackData)
	case strings.HasPrefix(callbackData, consts.CallbackSelectRedPacketTypeRandom):
		return handleSelectTypeRandom(ctx, cq, callbackData)
	case strings.HasPrefix(callbackData, consts.CallbackSelectRedPacketTypeFixed):
		return handleSelectTypeFixed(ctx, cq, callbackData)
	case callbackData == consts.CallbackRedPacketBackToTokenSelect:
		return handleBackToTokenSelect(ctx, cq)
	case strings.HasPrefix(callbackData, "rp:back_type:"):
		return handleBackToTypeSelect(ctx, cq, callbackData)
	case strings.HasPrefix(callbackData, "rp:back_quantity:"):
		return handleBackToQuantityInput(ctx, cq, callbackData)

	// --- Blessing Flow Handlers (Deprecated - blessing step removed) ---
	case callbackData == consts.CallbackRedPacketInputBlessing:
		return callback.NewAlertResponse(cq.ID, service.I18n().T(ctx, "{#InvalidOperationOrExpired}")), nil
	case callbackData == consts.CallbackRedPacketSkipBlessing:
		return callback.NewAlertResponse(cq.ID, service.I18n().T(ctx, "{#InvalidOperationOrExpired}")), nil
	case callbackData == consts.CallbackRedPacketConfirmBlessing:
		return callback.NewAlertResponse(cq.ID, service.I18n().T(ctx, "{#InvalidOperationOrExpired}")), nil
	case callbackData == consts.CallbackRedPacketReEnterBlessing:
		return callback.NewAlertResponse(cq.ID, service.I18n().T(ctx, "{#InvalidOperationOrExpired}")), nil

	// --- Other Submenu Handlers (Placeholder) ---
	// case callbackData == consts.CallbackRedPacketOngoing:
	//     return handleRedPacketOngoing(ctx, cq)
	// case callbackData == consts.CallbackRedPacketEnded:
	//     return handleRedPacketEnded(ctx, cq)

	// --- Payment Confirmation Flow Handlers ---
	case callbackData == localConsts.CallbackRedPacketConfirmPayment:
		return handleConfirmPayment(ctx, cq)
	case callbackData == localConsts.CallbackRedPacketSetCover:
		return handleSetCover(ctx, cq)
	case callbackData == localConsts.CallbackRedPacketBackToAmount:
		return handleBackToAmount(ctx, cq)
	case callbackData == localConsts.CallbackRedPacketBackToMain:
		return handleBackToMain(ctx, cq)
	case callbackData == localConsts.CallbackRedPacketBackToType:
		return handleBackToType(ctx, cq)

	// --- Cover Selection Flow Handlers ---
	case callbackData == consts.CallbackRedPacketSelectCoverConfirm:
		// User confirmed the cover (default or selected custom)
		return handleConfirmCoverSelection(ctx, cq)

	case callbackData == consts.CallbackRedPacketSelectCoverCustom:
		// User wants to select a custom cover
		return handleShowCustomCoverSelection(ctx, cq, 1) // Show first page

	case strings.HasPrefix(callbackData, consts.CallbackRedPacketSelectCoverCustomPagePrefix):
		// User is navigating custom cover pages
		pageStr := strings.TrimPrefix(callbackData, consts.CallbackRedPacketSelectCoverCustomPagePrefix)
		page := gconv.Int(pageStr)
		if page < 1 {
			page = 1
		}
		return handleShowCustomCoverSelection(ctx, cq, page)

	case callbackData == consts.CallbackRedPacketSelectCoverCustomSelectCurrent:
		// User selected the currently displayed custom cover
		// FileId will be retrieved from user state inside the handler
		return handleSelectCustomCover(ctx, cq) // Call without fileId

	case callbackData == consts.CallbackRedPacketSelectCoverCustomBack:
		// User wants to go back from custom selection to default view
		return handleReturnToDefaultCover(ctx, cq)

	// --- No Cover Handler ---
	case callbackData == "rp:no_cover":
		// User selected "不设置封面" option
		return handleNoCover(ctx, cq)

	// --- Final Confirmation Handlers ---
	case callbackData == consts.CallbackPrefixRedPacketFinalConfirm:
		return handleFinalConfirm(ctx, cq)

	case callbackData == consts.CallbackPrefixRedPacketFinalCancel:
		return handleFinalCancel(ctx, cq)

	// Red Packet Password handlers - handle the actual callback format from keyboard
	case strings.HasPrefix(callbackData, consts.CallbackRedPacketPasswordKeyPrefix):
		// Handle all password-related callbacks under this prefix
		keyAction := strings.TrimPrefix(callbackData, consts.CallbackRedPacketPasswordKeyPrefix)

		if keyAction == "_cancel" {
			return handleRedPacketPasswordCancel(ctx, cq, nil)
		} else if strings.HasPrefix(keyAction, "_confirm_") {
			return handleRedPacketPasswordConfirm(ctx, cq, nil)
		} else if strings.HasPrefix(keyAction, "_delete_") {
			return handleRedPacketPasswordDelete(ctx, cq, nil)
		} else if strings.HasPrefix(keyAction, "_key_") {
			return handleRedPacketPasswordKeyPress(ctx, cq, nil)
		} else {
			g.Log().Warningf(ctx, "HandleRedPacketSubmenuCallback: Unknown password action: %s", keyAction)
			return callback.NewAlertResponse(i18n.T(ctx, "{#UnknownOperation}")), nil
		}

	// Red Packet Details Page handlers
	case callbackData == "rp:back_to_type_selection":
		return handleBackToTypeSelection(ctx, cq)

	case callbackData == "rp:close_details":
		return handleCloseDetails(ctx, cq)

	case strings.HasPrefix(callbackData, "rp:back_to_list:"):
		// Parse format: rp:back_to_list:page:status
		parts := strings.Split(strings.TrimPrefix(callbackData, "rp:back_to_list:"), ":")
		if len(parts) < 2 {
			return callback.NewAlertResponse(i18n.T(ctx, "{#InvalidInput}")), nil
		}
		page := gconv.Int(parts[0])
		status := parts[1]
		return handleBackToRedPacketList(ctx, cq, page, status)

	case strings.HasPrefix(callbackData, "rp:details:"):
		// Parse format: rp:details:uuid:page:status
		parts := strings.Split(strings.TrimPrefix(callbackData, "rp:details:"), ":")
		if len(parts) < 1 {
			return callback.NewAlertResponse(i18n.T(ctx, "{#InvalidInput}")), nil
		}
		redPacketUUID := parts[0]
		page := 1
		status := "all"
		if len(parts) >= 3 {
			page = gconv.Int(parts[1])
			status = parts[2]
		}
		return handleRedPacketDetailsFromList(ctx, cq, redPacketUUID, page, status)

	case strings.HasPrefix(callbackData, "rp:set_memo:"):
		redPacketUUID := strings.TrimPrefix(callbackData, "rp:set_memo:")
		return handleSetMemo(ctx, cq, redPacketUUID)

	case strings.HasPrefix(callbackData, "rp:set_conditions:"):
		redPacketUUID := strings.TrimPrefix(callbackData, "rp:set_conditions:")
		return handleSetConditions(ctx, cq, redPacketUUID)

	case strings.HasPrefix(callbackData, "rp:back_to_details:"):
		redPacketUUID := strings.TrimPrefix(callbackData, "rp:back_to_details:")
		return handleBackToDetails(ctx, cq, redPacketUUID)

	// Condition toggle handlers
	case strings.HasPrefix(callbackData, "rp:toggle_verification:"):
		redPacketUUID := strings.TrimPrefix(callbackData, "rp:toggle_verification:")
		return handleToggleVerification(ctx, cq, redPacketUUID)

	case strings.HasPrefix(callbackData, "rp:toggle_premium:"):
		redPacketUUID := strings.TrimPrefix(callbackData, "rp:toggle_premium:")
		return handleTogglePremium(ctx, cq, redPacketUUID)

	case strings.HasPrefix(callbackData, "rp:set_group:"):
		redPacketUUID := strings.TrimPrefix(callbackData, "rp:set_group:")
		return handleSetGroup(ctx, cq, redPacketUUID)

	case strings.HasPrefix(callbackData, "rp:generate_invite:"):
		redPacketUUID := strings.TrimPrefix(callbackData, "rp:generate_invite:")
		return handleGenerateInvite(ctx, cq, redPacketUUID)

	case strings.HasPrefix(callbackData, "rp:delete_invite:"):
		redPacketUUID := strings.TrimPrefix(callbackData, "rp:delete_invite:")
		return handleDeleteInvite(ctx, cq, redPacketUUID)

	case strings.HasPrefix(callbackData, "rp:close_group:"):
		redPacketUUID := strings.TrimPrefix(callbackData, "rp:close_group:")
		return handleCloseGroup(ctx, cq, redPacketUUID)

	case strings.HasPrefix(callbackData, "rp:set_betting:"):
		redPacketUUID := strings.TrimPrefix(callbackData, "rp:set_betting:")
		return handleSetBetting(ctx, cq, redPacketUUID)

	case callbackData == "rp:noop":
		// No operation callback - show status emoji as toast notification
		return &callback.AlertResponse{CallbackQueryID: cq.ID, Text: "❌", ShowAlert: false}, nil

	case strings.HasPrefix(callbackData, "rp_verify:"):
		return handleVerificationAnswer(ctx, cq, callbackData)

	case strings.HasPrefix(callbackData, "rp_claim:"):
		return handleDirectClaim(ctx, cq, callbackData)

	// Betting volume handlers
	case strings.HasPrefix(callbackData, "rp:close_betting:"):
		redPacketUUID := strings.TrimPrefix(callbackData, "rp:close_betting:")
		return handleCloseBetting(ctx, cq, redPacketUUID)

	case strings.HasPrefix(callbackData, "rp:set_betting_time:"):
		redPacketUUID := strings.TrimPrefix(callbackData, "rp:set_betting_time:")
		return handleSetBettingTime(ctx, cq, redPacketUUID)

	case strings.HasPrefix(callbackData, "rp:select_betting_time:"):
		parts := strings.Split(strings.TrimPrefix(callbackData, "rp:select_betting_time:"), ":")
		if len(parts) == 2 {
			redPacketUUID := parts[0]
			days, err := strconv.Atoi(parts[1])
			if err != nil {
				g.Log().Errorf(ctx, "Invalid days format in callback data: %s", callbackData)
				return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#InvalidOperation}")), nil
			}
			return handleSelectBettingTime(ctx, cq, redPacketUUID, days)
		}
		return callback.NewAlertResponse(cq.ID, i18n.T(ctx, "{#InvalidOperation}")), nil

	// Group selection handler
	case strings.HasPrefix(callbackData, "rp:show_group_selection:"):
		redPacketUUID := strings.TrimPrefix(callbackData, "rp:show_group_selection:")
		return handleShowGroupSelection(ctx, cq, redPacketUUID)

	// Red Packet Cancel handler
	case callbackData == "rp_cancel":
		return HandleRedPacketCancelCallback(ctx, cq)

	// Red Packet Premium Toggle handlers
	case strings.HasPrefix(callbackData, "rp:set_premium:"):
		return handleSetPremium(ctx, cq, strings.Split(callbackData, ":"))
	case strings.HasPrefix(callbackData, "rp:cancel_premium:"):
		return handleCancelPremium(ctx, cq, strings.Split(callbackData, ":"))
	// Temporary backward compatibility for old format
	case strings.HasPrefix(callbackData, "rp_set_premium:"):
		// Convert old format to new format for processing
		parts := strings.Split(callbackData, ":")
		if len(parts) == 2 {
			return handleSetPremium(ctx, cq, []string{"rp", "set_premium", parts[1]})
		}
		return callback.NewAlertResponse(i18n.T(ctx, "{#InvalidOperation}")), nil
	case strings.HasPrefix(callbackData, "rp_cancel_premium:"):
		// Convert old format to new format for processing
		parts := strings.Split(callbackData, ":")
		if len(parts) == 2 {
			return handleCancelPremium(ctx, cq, []string{"rp", "cancel_premium", parts[1]})
		}
		return callback.NewAlertResponse(i18n.T(ctx, "{#InvalidOperation}")), nil

	default:
		// Unknown callback data within the red packet scope
		g.Log().Warningf(ctx, "HandleRedPacketSubmenuCallback: Received unknown callback data: %s", callbackData)
		// Assuming NewAlertResponse exists in callback package
		// If not, use tgbotapi.NewCallback directly or adjust
		alertResp := callback.NewAlertResponse(i18n.T(ctx, "{#UnknownOperation}")) // Assuming this constructor exists
		if alertResp == nil {
			// Fallback or handle error if constructor is missing/fails
			g.Log().Error(ctx, "Failed to create alert response for unknown operation")
			return nil, nil // Avoid panic
		}
		return alertResp, nil
	}
}

// handleCloseClaimList handles the close claim list callback - deletes the claim list message
func handleCloseClaimList(ctx context.Context, cq *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	// Check if we have message context
	if cq.Message == nil || cq.Message.Chat == nil {
		// If no message context, just answer the callback
		return callback.NewAnswerCallback(cq.ID, ""), nil
	}

	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID

	// Create delete message response
	response := &callback.DeleteMessageResponse{
		CallbackQueryID: cq.ID,
		ChatID:          chatID,
		MessageID:       messageID,
	}

	return response, nil
}

// Placeholder for the old function, if needed for the specific prefix case
// Note: This might still conflict if handleShowCoverPage is defined elsewhere in the package.
// If the original handleShowCoverPage is in cover_handler_show_page.go, it should be removed or renamed.
// func handleShowCoverPage(ctx context.Context, cq *tgbotapi.CallbackQuery, page int) (callback.CallbackResponse, error) {
// 	g.Log().Infof(ctx, "Placeholder: handleShowCoverPage (page %d) for user %d", page, cq.From.ID)
// 	i18n := service.I18n().Instance()
// 	// Assuming NewAlertResponse exists
// 	alertResp := callback.NewAlertResponse(i18n.T(ctx, "{#NotImplementedYet}"))
// 	if alertResp == nil {
// 		g.Log().Error(ctx, "Failed to create alert response for handleShowCoverPage placeholder")
// 		return nil, nil
// 	}
// 	return alertResp, nil
// }

// Removed all other placeholder function definitions as they exist in other files.

// handleBackToPaymentConfirmation handles returning to payment confirmation page from cover selection
func handleBackToPaymentConfirmation(ctx context.Context, cq *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	i18n := service.I18n().Instance()
	telegramUserID := cq.From.ID

	// Get user state to retrieve red packet ID
	state, err := service.UserState().GetUserStateByTelegramId(ctx, telegramUserID)
	if err != nil || state == nil || state.Context == nil {
		g.Log().Errorf(ctx, "handleBackToPaymentConfirmation: Failed to get user state for user %d: %v", telegramUserID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#ErrorStateExpiredOrInvalid}")), nil
	}

	redPacketIDStr, ok := state.Context["red_packet_id"]
	if !ok || redPacketIDStr == "" {
		g.Log().Errorf(ctx, "handleBackToPaymentConfirmation: No red packet ID found in user state for user %d", telegramUserID)
		return callback.NewAlertResponse(i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}

	redPacketID, err := strconv.ParseInt(redPacketIDStr, 10, 64)
	if err != nil {
		g.Log().Errorf(ctx, "handleBackToPaymentConfirmation: Invalid red packet ID format %s for user %d: %v", redPacketIDStr, telegramUserID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}

	// Use unified function to show payment confirmation page
	return ShowPaymentConfirmationPage(ctx, cq, redPacketID, telegramUserID)
}
