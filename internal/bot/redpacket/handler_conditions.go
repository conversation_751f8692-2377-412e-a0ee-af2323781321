package redpacket

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	localConsts "telegram-bot-api/internal/consts"
	"telegram-bot-api/internal/dao"
	"telegram-bot-api/internal/middleware"
	"telegram-bot-api/internal/model"
	"telegram-bot-api/internal/model/callback"
	"telegram-bot-api/internal/model/entity"
	"telegram-bot-api/internal/service"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// handleToggleVerification toggles the verification code requirement for a red packet
func handleToggleVerification(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacketUUID string) (callback.CallbackResponse, error) {
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// Get red packet record
	var redPacket *entity.RedPackets
	err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Scan(&redPacket)

	if err != nil || redPacket == nil {
		g.Log().Errorf(ctx, "handleToggleVerification: Failed to get red packet %s for user %d: %v", redPacketUUID, userID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}

	// Toggle the verification code status
	newStatus := 0
	if redPacket.IsVerificationCode == 0 {
		newStatus = 1
	}

	// Update the database
	_, err = dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Data(g.Map{"is_verification_code": newStatus}).
		Update()

	if err != nil {
		g.Log().Errorf(ctx, "handleToggleVerification: Failed to update verification status for red packet %s: %v", redPacketUUID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), nil
	}

	// Update the red packet object
	redPacket.IsVerificationCode = newStatus

	// Refresh the conditions page
	return refreshConditionsPage(ctx, cq, redPacket)
}

// handleTogglePremium toggles the premium requirement for a red packet
func handleTogglePremium(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacketUUID string) (callback.CallbackResponse, error) {
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// Get red packet record
	var redPacket *entity.RedPackets
	err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Scan(&redPacket)

	if err != nil || redPacket == nil {
		g.Log().Errorf(ctx, "handleTogglePremium: Failed to get red packet %s for user %d: %v", redPacketUUID, userID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}

	// Toggle the premium status
	newStatus := 0
	if redPacket.IsPremium == 0 {
		newStatus = 1
	}

	// Update the database
	_, err = dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Data(g.Map{"is_premium": newStatus}).
		Update()

	if err != nil {
		g.Log().Errorf(ctx, "handleTogglePremium: Failed to update premium status for red packet %s: %v", redPacketUUID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), nil
	}

	// Update the red packet object
	redPacket.IsPremium = newStatus

	// Refresh the conditions page
	return refreshConditionsPage(ctx, cq, redPacket)
}

// handleSetGroup handles setting specific group for a red packet
func handleSetGroup(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacketUUID string) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	g.Log().Infof(ctx, "[GROUP_SELECT] handleSetGroup called - UserID: %d, ChatID: %d, RedPacketUUID: %s",
		userID, chatID, redPacketUUID)

	// Get red packet record to verify ownership
	//g.Log().Debugf(ctx, "[GROUP_SELECT] Verifying ownership of red packet %s for user %d", redPacketUUID, userID)
	var redPacket *entity.RedPackets
	err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Scan(&redPacket)

	if err != nil || redPacket == nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to get red packet %s for user %d: %v", redPacketUUID, userID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}
	g.Log().Infof(ctx, "[GROUP_SELECT] Successfully verified ownership of red packet %s", redPacketUUID)

	// Build group selection page
	text := fmt.Sprintf(
		"👥 <b>%s</b>",
		i18n.T(ctx, "指定群组红包"),
	)

	// If a group is already selected, show the group info
	if redPacket.GroupId != "" && redPacket.GroupId != "0" {
		// Get the tenant ID from context
		tenantId, ok := middleware.GetTenantIdFromContext(ctx)
		if ok {
			// Try to get the group info
			var telegramGroup *entity.TelegramGroups
			groupChatId, _ := strconv.ParseInt(redPacket.GroupId, 10, 64)
			err = dao.TelegramGroups.Ctx(ctx).
				Where("chat_id", groupChatId).
				Where("tenant_id", tenantId).
				Scan(&telegramGroup)

			if err == nil && telegramGroup != nil {
				text = fmt.Sprintf(
					"👥 <b>%s</b>\n\n%s: %s",
					i18n.T(ctx, "指定群组红包"),
					i18n.T(ctx, "群组"),
					telegramGroup.Title,
				)

				// Add invitation link if it exists
				if redPacket.GroupInvitationLink != "" {
					text += fmt.Sprintf("\n%s: %s",
						i18n.T(ctx, "邀请链接"),
						redPacket.GroupInvitationLink,
					)
				}
			}
		}
	}

	// Create keyboard with options
	//g.Log().Debugf(ctx, "[GROUP_SELECT] Building group selection menu for red packet %s", redPacketUUID)
	var keyboardRows [][]tgbotapi.InlineKeyboardButton
	
	// First row: Select group button
	keyboardRows = append(keyboardRows, tgbotapi.NewInlineKeyboardRow(
		tgbotapi.NewInlineKeyboardButtonData(
			"📤 "+i18n.T(ctx, "选择群组"),
			fmt.Sprintf("rp:show_group_selection:%s", redPacketUUID),
		),
	))
	
	// Second row: Generate or Delete invite link button
	if redPacket.GroupInvitationLink != "" {
		// Show delete button if invite link exists
		keyboardRows = append(keyboardRows, tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"🗑️ "+i18n.T(ctx, "删除邀请链接"),
				fmt.Sprintf("rp:delete_invite:%s", redPacketUUID),
			),
		))
	} else if redPacket.GroupId != "" && redPacket.GroupId != "0" {
		// Show generate button only if group is selected but no invite link
		keyboardRows = append(keyboardRows, tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"🔗 "+i18n.T(ctx, "生成邀请链接"),
				fmt.Sprintf("rp:generate_invite:%s", redPacketUUID),
			),
		))
	}
	
	// Third row: Back button
	keyboardRows = append(keyboardRows, tgbotapi.NewInlineKeyboardRow(
		tgbotapi.NewInlineKeyboardButtonData(
			"↩️ "+i18n.T(ctx, "返回"),
			fmt.Sprintf("rp:set_conditions:%s", redPacketUUID),
		),
	))
	
	keyboard := tgbotapi.NewInlineKeyboardMarkup(keyboardRows...)

	// Delete current message and send new one
	g.Log().Infof(ctx, "[GROUP_SELECT] Sending group selection menu to user %d", userID)
	response := callback.NewDeleteAndSendResponse(chatID, messageID, text, &keyboard)
	response.CallbackQueryID = cq.ID
	response.ParseMode = "HTML"

	g.Log().Infof(ctx, "[GROUP_SELECT] Successfully prepared group selection menu for user %d", userID)
	return response, nil
}

// handleShowGroupSelection shows the group selection reply keyboard
func handleShowGroupSelection(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacketUUID string) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	g.Log().Infof(ctx, "[GROUP_SELECT] handleShowGroupSelection called - UserID: %d, ChatID: %d, RedPacketUUID: %s",
		userID, chatID, redPacketUUID)

	// Get red packet record to verify ownership
	//g.Log().Debugf(ctx, "[GROUP_SELECT] Fetching red packet %s for user %d", redPacketUUID, userID)
	var redPacket *entity.RedPackets
	err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Scan(&redPacket)

	if err != nil || redPacket == nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to get red packet %s for user %d: %v", redPacketUUID, userID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}
	g.Log().Infof(ctx, "[GROUP_SELECT] Successfully fetched red packet %s", redPacketUUID)

	// Store the red packet UUID in Redis with 10 minutes expiration
	stateKey := fmt.Sprintf("group_select_state:%d", userID)
	err = g.Redis().SetEX(ctx, stateKey, redPacketUUID, 600) // 10 minutes = 600 seconds
	if err != nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to set Redis state for user %d: %v", userID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), nil
	}
	g.Log().Infof(ctx, "[GROUP_SELECT] Stored red packet UUID in Redis with key %s", stateKey)

	// Create reply keyboard with group selection button using KeyboardButtonRequestChat
	//g.Log().Debugf(ctx, "[GROUP_SELECT] Creating KeyboardButtonRequestChat with RequestID: 1, ChatIsChannel: false, RequestTitle: true, RequestUsername: true")
	requestChat := tgbotapi.KeyboardButtonRequestChat{
		RequestID:       1,
		ChatIsChannel:   false, // Request groups/supergroups, not channels
		RequestTitle:    true,  // Request the chat's title
		RequestUsername: true,  // Request the chat's username
	}
	groupRequestButton := tgbotapi.NewKeyboardButtonRequestChat("👥 "+i18n.T(ctx, "群组"), requestChat)
	replyKeyboard := tgbotapi.NewReplyKeyboard(
		tgbotapi.NewKeyboardButtonRow(groupRequestButton),
	)
	replyKeyboard.ResizeKeyboard = true
	replyKeyboard.OneTimeKeyboard = true

	// Message text
	text := "👥 " + i18n.T(ctx, "点击下方按钮选择群组")

	// Create response with reply keyboard
	g.Log().Infof(ctx, "[GROUP_SELECT] Creating reply keyboard response for user %d", userID)
	response := callback.NewDeleteAndSendWithReplyKeyboardResponse(chatID, messageID, text, &replyKeyboard)
	response.CallbackQueryID = cq.ID

	g.Log().Infof(ctx, "[GROUP_SELECT] Successfully prepared group selection keyboard for user %d", userID)
	return response, nil
}

// handleSetBetting handles showing the betting time selection page directly
func handleSetBetting(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacketUUID string) (callback.CallbackResponse, error) {
	// Directly go to time selection page
	return handleSetBettingTime(ctx, cq, redPacketUUID)
}

// GetBettingTimeText returns the display text for betting time
func GetBettingTimeText(ctx context.Context, days int) string {
	i18n := service.I18n().Instance()

	switch days {
	case 0:
		return i18n.T(ctx, "总流水")
	case 1:
		return i18n.T(ctx, "今日")
	case 7:
		return i18n.T(ctx, "近7天")
	case 30:
		return i18n.T(ctx, "近30天")
	case -1:
		return i18n.T(ctx, "本月")
	default:
		return fmt.Sprintf(i18n.T(ctx, "近%d天"), days)
	}
}

// handleCloseGroup handles closing/disabling group requirement
func handleCloseGroup(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacketUUID string) (callback.CallbackResponse, error) {
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// Get red packet record
	var redPacket *entity.RedPackets
	err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Scan(&redPacket)

	if err != nil || redPacket == nil {
		g.Log().Errorf(ctx, "handleCloseGroup: Failed to get red packet %s for user %d: %v", redPacketUUID, userID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}

	// Update database to clear group settings
	_, err = dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Data(g.Map{
			"group_id":              "",
			"group_invitation_link": "",
			"specify_group":         0,
		}).
		Update()

	if err != nil {
		g.Log().Errorf(ctx, "handleCloseGroup: Failed to update group settings for red packet %s: %v", redPacketUUID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), nil
	}

	// Update the red packet object
	redPacket.GroupId = ""
	redPacket.GroupInvitationLink = ""
	redPacket.SpecifyGroup = 0

	// Refresh the conditions page
	return refreshConditionsPage(ctx, cq, redPacket)
}

// handleCloseBetting handles closing/disabling betting volume requirement
func handleCloseBetting(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacketUUID string) (callback.CallbackResponse, error) {
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// Get red packet record
	var redPacket *entity.RedPackets
	err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Scan(&redPacket)

	if err != nil || redPacket == nil {
		g.Log().Errorf(ctx, "handleCloseBetting: Failed to get red packet %s for user %d: %v", redPacketUUID, userID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}

	// Update database to clear betting settings
	_, err = dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Data(g.Map{
			"betting_volume":      0,
			"betting_volume_days": 0,
			"specify_betting":     0,
		}).
		Update()

	if err != nil {
		g.Log().Errorf(ctx, "handleCloseBetting: Failed to update betting settings for red packet %s: %v", redPacketUUID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), nil
	}

	// Update the red packet object
	redPacket.BettingVolume = decimal.Zero
	redPacket.BettingVolumeDays = 0
	redPacket.SpecifyBetting = 0

	// Refresh the conditions page
	return refreshConditionsPage(ctx, cq, redPacket)
}

// handleSetBettingTime handles showing the betting time selection page
func handleSetBettingTime(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacketUUID string) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	i18n := service.I18n().Instance()

	// Build time selection page
	text := fmt.Sprintf(
		"⏰ <b>%s</b>",
		i18n.T(ctx, "请选择流水红包时间"),
	)

	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				i18n.T(ctx, "今日"),
				fmt.Sprintf("rp:select_betting_time:%s:1", redPacketUUID),
			),
			tgbotapi.NewInlineKeyboardButtonData(
				i18n.T(ctx, "近7天"),
				fmt.Sprintf("rp:select_betting_time:%s:7", redPacketUUID),
			),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				i18n.T(ctx, "近30天"),
				fmt.Sprintf("rp:select_betting_time:%s:30", redPacketUUID),
			),
			tgbotapi.NewInlineKeyboardButtonData(
				i18n.T(ctx, "本月"),
				fmt.Sprintf("rp:select_betting_time:%s:-1", redPacketUUID),
			),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				i18n.T(ctx, "总流水"),
				fmt.Sprintf("rp:select_betting_time:%s:0", redPacketUUID),
			),
		),
		tgbotapi.NewInlineKeyboardRow(
			// tgbotapi.NewInlineKeyboardButtonData(
			// 	"❌ "+i18n.T(ctx, "关闭"),
			// 	fmt.Sprintf("rp:close_betting:%s", redPacketUUID),
			// ),
			tgbotapi.NewInlineKeyboardButtonData(
				"↩️ "+i18n.T(ctx, "返回"),
				fmt.Sprintf("rp:set_conditions:%s", redPacketUUID),
			),
		),
	)

	// Delete current message and send new one
	response := callback.NewDeleteAndSendResponse(chatID, messageID, text, &keyboard)
	response.CallbackQueryID = cq.ID
	response.ParseMode = "HTML"
	return response, nil
}

// handleSelectBettingTime handles selecting a betting time period
func handleSelectBettingTime(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacketUUID string, days int) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// Update the betting_volume_days in database
	_, err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Data(g.Map{"betting_volume_days": days}).
		Update()

	if err != nil {
		g.Log().Errorf(ctx, "handleSelectBettingTime: Failed to update betting days for red packet %s: %v", redPacketUUID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), nil
	}

	// Set user state for amount input
	stateContext := map[string]string{
		"red_packet_uuid": redPacketUUID,
		"betting_days":    fmt.Sprintf("%d", days),
	}
	newState := model.NewUserState(localConsts.UserStateWaitingBettingAmount, stateContext, model.InputTypeText, 3, 600)
	errSet := service.UserState().SetUserStateByTelegramId(ctx, userID, newState)
	if errSet != nil {
		g.Log().Errorf(ctx, "handleSelectBettingTime: Failed to set user state for user %d: %v", userID, errSet)
		return callback.NewAlertResponse(i18n.T(ctx, "{#SetUserStateError}")), nil
	}

	// Build amount input prompt
	promptText := i18n.T(ctx, "请输入流水红包金额(CNY)")

	// Create keyboard with back button
	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"↩️ "+i18n.T(ctx, "返回"),
				fmt.Sprintf("rp:set_betting_time:%s", redPacketUUID),
			),
		),
	)

	// Delete current message and send new prompt
	response := callback.NewDeleteAndSendResponse(chatID, messageID, promptText, &keyboard)
	response.CallbackQueryID = cq.ID
	return response, nil
}

// handleGenerateInvite handles the "生成邀请链接" button
func handleGenerateInvite(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacketUUID string) (callback.CallbackResponse, error) {
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// Get red packet record
	var redPacket *entity.RedPackets
	err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Scan(&redPacket)

	if err != nil || redPacket == nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to get red packet %s for user %d: %v", redPacketUUID, userID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}

	// Check if group is selected
	if redPacket.GroupId == "" || redPacket.GroupId == "0" {
		return callback.NewAlertResponse(i18n.T(ctx, "❌ 请先选择群组")), nil
	}

	// Get tenant ID from context
	tenantId, ok := middleware.GetTenantIdFromContext(ctx)
	if !ok {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to get tenant ID from context")
		return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), nil
	}

	// Get group info
	var telegramGroup *entity.TelegramGroups
	groupChatId, _ := strconv.ParseInt(redPacket.GroupId, 10, 64)
	err = dao.TelegramGroups.Ctx(ctx).
		Where("chat_id", groupChatId).
		Where("tenant_id", tenantId).
		Scan(&telegramGroup)

	if err != nil || telegramGroup == nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to get telegram group %s: %v", redPacket.GroupId, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), nil
	}

	// Check if group has username (public group)
	if telegramGroup.Username == "" {
		g.Log().Infof(ctx, "[GROUP_SELECT] Group %d does not have username, cannot generate invitation link", groupChatId)
		return callback.NewAlertResponse(i18n.T(ctx, "❌群组不是公开群")), nil
	}

	// Generate invitation link
	inviteLink := fmt.Sprintf("https://t.me/%s", telegramGroup.Username)
	g.Log().Infof(ctx, "[GROUP_SELECT] Generated invitation link for group %d: %s", groupChatId, inviteLink)

	// Update red packet with invitation link
	_, err = dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Data(g.Map{"group_invitation_link": inviteLink}).
		Update()

	if err != nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to update invitation link for red packet %s: %v", redPacketUUID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), nil
	}

	// Update the red packet object
	redPacket.GroupInvitationLink = inviteLink

	// Refresh the group selection page
	return handleSetGroup(ctx, cq, redPacketUUID)
}

// handleDeleteInvite handles the "删除邀请链接" button
func handleDeleteInvite(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacketUUID string) (callback.CallbackResponse, error) {
	userID := cq.From.ID
	i18n := service.I18n().Instance()

	// Get red packet record
	var redPacket *entity.RedPackets
	err := dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Scan(&redPacket)

	if err != nil || redPacket == nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to get red packet %s for user %d: %v", redPacketUUID, userID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#ErrorRedPacketRecordNotFound}")), nil
	}

	// Clear the invitation link and group information
	_, err = dao.RedPackets.Ctx(ctx).
		Where("uuid", redPacketUUID).
		Where("creator_user_id", userID).
		Data(g.Map{
			"group_invitation_link": "",
			"group_id":              "",
			"specify_group":         0,
		}).
		Update()

	if err != nil {
		g.Log().Errorf(ctx, "[GROUP_SELECT] Failed to delete invitation link for red packet %s: %v", redPacketUUID, err)
		return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), nil
	}

	// Update the red packet object
	redPacket.GroupInvitationLink = ""
	redPacket.GroupId = ""
	redPacket.SpecifyGroup = 0

	g.Log().Infof(ctx, "[GROUP_SELECT] Successfully deleted invitation link for red packet %s", redPacketUUID)

	// Refresh the group selection page
	return handleSetGroup(ctx, cq, redPacketUUID)
}

// refreshConditionsPage refreshes the conditions page after a toggle
func refreshConditionsPage(ctx context.Context, cq *tgbotapi.CallbackQuery, redPacket *entity.RedPackets) (callback.CallbackResponse, error) {
	chatID := cq.Message.Chat.ID
	messageID := cq.Message.MessageID

	// Build updated conditions page
	text := buildRedPacketConditionsText(ctx, redPacket)
	keyboard := buildRedPacketConditionsKeyboard(ctx, redPacket)

	// Edit the current message
	response := callback.NewExtendedEditMessageResponse(cq.ID, chatID, messageID, text, &keyboard)
	response.ParseMode = "HTML"

	// Show a success notification
	i18n := service.I18n().Instance()
	response.CallbackQueryText = i18n.T(ctx, "✅ 设置已更新")
	response.ShowAlert = false

	return response, nil
}

// handleVerificationAnswer handles verification code answer submission
func handleVerificationAnswer(ctx context.Context, cq *tgbotapi.CallbackQuery, callbackData string) (callback.CallbackResponse, error) {
	i18n := service.I18n().Instance()
	userID := cq.From.ID

	// Parse callback data first: rp_verify:UUID:question:correctAnswer:userAnswer
	parts := strings.Split(callbackData, ":")
	if len(parts) != 5 {
		g.Log().Errorf(ctx, "handleVerificationAnswer: Invalid callback data format: %s", callbackData)
		return &callback.AlertResponse{
			CallbackQueryID: cq.ID,
			Text:            i18n.T(ctx, "{#SystemError}"),
			ShowAlert:       true,
		}, nil
	}

	redPacketUUID := parts[1]

	// Check for frequent clicking (3 seconds debounce) - use red packet UUID to prevent bypassing with different answers
	debounceKey := fmt.Sprintf("%s:verify_attempt", redPacketUUID)
	isDuplicate, err := service.Debounce().CheckWithWindow(ctx, userID, "rp_verify", debounceKey, 3*time.Second)
	if err != nil {
		g.Log().Errorf(ctx, "handleVerificationAnswer: Debounce check failed for user %d: %v", userID, err)
		// Continue with operation even if debounce check fails
	} else if isDuplicate {
		g.Log().Debugf(ctx, "handleVerificationAnswer: Frequent clicking detected for user %d on red packet %s", userID, redPacketUUID)
		return &callback.AlertResponse{
			CallbackQueryID: cq.ID,
			Text:            "❌操作频繁，请稍后再试",
			ShowAlert:       true,
		}, nil
	}

	question := parts[2]
	correctAnswerStr := parts[3]
	userAnswerStr := parts[4]

	// Parse answers
	correctAnswer, err := strconv.Atoi(correctAnswerStr)
	if err != nil {
		g.Log().Errorf(ctx, "handleVerificationAnswer: Failed to parse correct answer %s: %v", correctAnswerStr, err)
		return &callback.AlertResponse{
			CallbackQueryID: cq.ID,
			Text:            i18n.T(ctx, "{#SystemError}"),
			ShowAlert:       true,
		}, nil
	}

	userAnswer, err := strconv.Atoi(userAnswerStr)
	if err != nil {
		g.Log().Errorf(ctx, "handleVerificationAnswer: Failed to parse user answer %s: %v", userAnswerStr, err)
		return &callback.AlertResponse{
			CallbackQueryID: cq.ID,
			Text:            i18n.T(ctx, "{#SystemError}"),
			ShowAlert:       true,
		}, nil
	}

	// Check if user is currently banned from claiming red packets
	isBanned, err := service.RedPacketBan().IsUserBanned(ctx, userID)
	if err != nil {
		g.Log().Warningf(ctx, "handleVerificationAnswer: Failed to check ban status for user %d: %v", userID, err)
	} else if isBanned {
		g.Log().Infof(ctx, "handleVerificationAnswer: User %d is banned from claiming red packets", userID)
		return &callback.AlertResponse{
			CallbackQueryID: cq.ID,
			Text:            "❌24小时内不可以领取红包",
			ShowAlert:       true,
		}, nil
	}

	// Check if red packet requires Premium membership
	redPacket, err := service.RedPacket().GetRedPacketByUUID(ctx, redPacketUUID)
	if err != nil {
		g.Log().Errorf(ctx, "handleVerificationAnswer: Failed to get red packet %s: %v", redPacketUUID, err)
		return &callback.AlertResponse{
			CallbackQueryID: cq.ID,
			Text:            i18n.T(ctx, "{#SystemError}"),
			ShowAlert:       true,
		}, nil
	}
	if redPacket == nil {
		g.Log().Errorf(ctx, "handleVerificationAnswer: Red packet %s not found", redPacketUUID)
		return &callback.AlertResponse{
			CallbackQueryID: cq.ID,
			Text:            i18n.T(ctx, "{#RedPacketNotFound}"),
			ShowAlert:       true,
		}, nil
	}

	if redPacket.IsPremium == 1 {
		g.Log().Infof(ctx, "handleVerificationAnswer: Red packet %s requires Premium membership, checking user %d", redPacketUUID, userID)

		// Use the is_premium field from the Telegram User object directly
		isPremium := cq.From.IsPremium

		if !isPremium {
			g.Log().Warningf(ctx, "handleVerificationAnswer: User %d is not premium (from Telegram User object), cannot claim premium red packet %s", userID, redPacketUUID)
			return &callback.AlertResponse{
				CallbackQueryID: cq.ID,
				Text:            i18n.T(ctx, "{#RedPacketPremiumRequired}"),
				ShowAlert:       true,
			}, nil
		}

		g.Log().Infof(ctx, "handleVerificationAnswer: User %d has premium status (from Telegram User object), can claim premium red packet %s", userID, redPacketUUID)
	}

	// Check if answer is correct
	if userAnswer == correctAnswer {
		// Correct answer - clear error count and proceed to claim red packet
		g.Log().Infof(ctx, "handleVerificationAnswer: User %d answered correctly for red packet %s", userID, redPacketUUID)

		// Clear error count for this red packet since user answered correctly
		service.RedPacketBan().ClearUserErrors(ctx, userID, redPacketUUID)

		// Get user info for username
		receiverUsername := cq.From.UserName
		if receiverUsername == "" {
			receiverUsername = cq.From.FirstName
		}

		// Call the verification claim method
		result, claimErr := service.RedPacket().ClaimRedPacketWithVerification(ctx, userID, redPacketUUID, receiverUsername, question, correctAnswer, userAnswer)

		if claimErr != nil {
			g.Log().Errorf(ctx, "handleVerificationAnswer: Failed to claim red packet %s for user %d: %v", redPacketUUID, userID, claimErr)
			return &callback.AlertResponse{
				CallbackQueryID: cq.ID,
				Text:            claimErr.Error(),
				ShowAlert:       true,
			}, nil
		}

		// Success message
		successMessage := fmt.Sprintf("✅ %s\n\n🎉 %s: %s %s",
			i18n.T(ctx, "答案正确！"),
			i18n.T(ctx, "恭喜您获得"),
			result.Amount.String(),
			result.Symbol)

		return &callback.AlertResponse{
			CallbackQueryID: cq.ID,
			Text:            successMessage,
			ShowAlert:       true,
		}, nil
	} else {
		// Wrong answer - increment error count for this red packet
		g.Log().Infof(ctx, "handleVerificationAnswer: User %d answered incorrectly for red packet %s (correct: %d, user: %d)",
			userID, redPacketUUID, correctAnswer, userAnswer)

		// Track errors per red packet to prevent abuse of single red packet
		redisClient := service.Redis().Client()
		errorKey := fmt.Sprintf("rp_errors:%d:%s", userID, redPacketUUID)
		errorCount, err := redisClient.Incr(ctx, errorKey)
		if err != nil {
			g.Log().Errorf(ctx, "handleVerificationAnswer: Failed to increment error count for user %d: %v", userID, err)
		} else {
			// Set expiration for error count (24 hours)
			redisClient.Expire(ctx, errorKey, 24*60*60)

			// Check if user has made 2 consecutive errors on this red packet
			if errorCount >= 2 {
				// Ban user for 24 hours using the service
				banErr := service.RedPacketBan().BanUser(ctx, userID, 24*time.Hour, "连续验证错误")
				if banErr != nil {
					g.Log().Errorf(ctx, "handleVerificationAnswer: Failed to ban user %d: %v", userID, banErr)
				}

				g.Log().Warningf(ctx, "handleVerificationAnswer: User %d banned for 24 hours due to %d consecutive errors on red packet %s",
					userID, errorCount, redPacketUUID)

				return &callback.AlertResponse{
					CallbackQueryID: cq.ID,
					Text:            "❌24小时内不可以领取红包",
					ShowAlert:       true,
				}, nil
			}
		}

		return &callback.AlertResponse{
			CallbackQueryID: cq.ID,
			Text: fmt.Sprintf("❌ %s",
				i18n.T(ctx, "答案错误，请重试"),
			),
			ShowAlert: true,
		}, nil
	}
}

// handleDirectClaim handles direct red packet claiming via callback button
func handleDirectClaim(ctx context.Context, cq *tgbotapi.CallbackQuery, callbackData string) (callback.CallbackResponse, error) {
	i18n := service.I18n().Instance()
	userID := cq.From.ID

	// Parse callback data first: rp_claim:UUID
	parts := strings.Split(callbackData, ":")
	if len(parts) != 2 {
		g.Log().Errorf(ctx, "handleDirectClaim: Invalid callback data format: %s", callbackData)
		return callback.NewAlertResponse(i18n.T(ctx, "{#SystemError}")), nil
	}

	redPacketUUID := parts[1]

	// Check if user is currently banned from claiming red packets
	isBanned, err := service.RedPacketBan().IsUserBanned(ctx, userID)
	if err != nil {
		g.Log().Warningf(ctx, "handleDirectClaim: Failed to check ban status for user %d: %v", userID, err)
	} else if isBanned {
		g.Log().Infof(ctx, "handleDirectClaim: User %d is banned from claiming red packets", userID)
		return &callback.AlertResponse{
			CallbackQueryID: cq.ID,
			Text:            "❌24小时内不可以领取红包",
			ShowAlert:       true,
		}, nil
	}

	// Check if red packet requires Premium membership
	redPacket, err := service.RedPacket().GetRedPacketByUUID(ctx, redPacketUUID)
	if err != nil {
		g.Log().Errorf(ctx, "handleDirectClaim: Failed to get red packet %s: %v", redPacketUUID, err)
		return &callback.AlertResponse{
			CallbackQueryID: cq.ID,
			Text:            i18n.T(ctx, "{#SystemError}"),
			ShowAlert:       true,
		}, nil
	}
	if redPacket == nil {
		g.Log().Errorf(ctx, "handleDirectClaim: Red packet %s not found", redPacketUUID)
		return &callback.AlertResponse{
			CallbackQueryID: cq.ID,
			Text:            i18n.T(ctx, "{#RedPacketNotFound}"),
			ShowAlert:       true,
		}, nil
	}

	if redPacket.IsPremium == 1 {
		g.Log().Infof(ctx, "handleDirectClaim: Red packet %s requires Premium membership, checking user %d", redPacketUUID, userID)

		// Use the is_premium field from the Telegram User object directly
		isPremium := cq.From.IsPremium

		if !isPremium {
			g.Log().Warningf(ctx, "handleDirectClaim: User %d is not premium (from Telegram User object), cannot claim premium red packet %s", userID, redPacketUUID)
			return &callback.AlertResponse{
				CallbackQueryID: cq.ID,
				Text:            i18n.T(ctx, "{#RedPacketPremiumRequired}"),
				ShowAlert:       true,
			}, nil
		}

		g.Log().Infof(ctx, "handleDirectClaim: User %d has premium status (from Telegram User object), can claim premium red packet %s", userID, redPacketUUID)
	}

	g.Log().Infof(ctx, "handleDirectClaim: User %d attempting to claim red packet %s", userID, redPacketUUID)

	// Get user info for username
	receiverUsername := cq.From.UserName
	if receiverUsername == "" {
		receiverUsername = cq.From.FirstName
	}

	// Call the normal claim method
	result, claimErr := service.RedPacket().ClaimRedPacket(ctx, userID, redPacketUUID, receiverUsername)

	if claimErr != nil {
		g.Log().Errorf(ctx, "handleDirectClaim: Failed to claim red packet %s for user %d: %v", redPacketUUID, userID, claimErr)
		return &callback.AlertResponse{
			CallbackQueryID: cq.ID,
			Text:            claimErr.Error(),
			ShowAlert:       true,
		}, nil
	}

	// Success message
	successMessage := fmt.Sprintf("🎉 %s: %s %s",
		i18n.T(ctx, "恭喜您获得"),
		result.Amount.String(),
		result.Symbol)

	g.Log().Infof(ctx, "handleDirectClaim: Successfully claimed red packet %s for user %d. Amount: %s %s",
		redPacketUUID, userID, result.Amount.String(), result.Symbol)

	return &callback.AlertResponse{
		CallbackQueryID: cq.ID,
		Text:            successMessage,
		ShowAlert:       true,
	}, nil
}
