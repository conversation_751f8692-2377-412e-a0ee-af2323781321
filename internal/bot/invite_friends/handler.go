package invite_friends

import (
	"context"
	"fmt"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/shopspring/decimal"

	"telegram-bot-api/internal/bot/shared"
	"telegram-bot-api/internal/config"
	"telegram-bot-api/internal/dao"
	"telegram-bot-api/internal/middleware"
	"telegram-bot-api/internal/model/callback"
	"telegram-bot-api/internal/model/entity"
	"telegram-bot-api/internal/service"
)

const (
	SubordinatesPerPage = 10
)

func HandleInviteFriendsMenu(ctx context.Context, query *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	g.Log().Infof(ctx, "Invite friends clicked by user %d", query.From.ID)

	i18n := service.I18n().Instance()

	// Get user data
	user, err := service.User().GetUserByTelegramId(ctx, query.From.ID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get user: %v", err)
		return nil, err
	}

	// Get tenant ID from context
	tenantId, ok := middleware.GetTenantIdFromContext(ctx)
	if !ok {
		g.Log().Error(ctx, "Failed to get tenant ID from context")
		return nil, fmt.Errorf("tenant ID not found in context")
	}

	// Get bot instance from TenantBotManager
	bot, err := service.TenantBotManager().GetBot(ctx, tenantId)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get bot instance for tenant %d: %v", tenantId, err)
		return nil, fmt.Errorf("failed to get bot instance: %w", err)
	}

	// Generate invite link
	inviteLink := fmt.Sprintf("https://t.me/%s?start=%s", bot.Self.UserName, user.InviteCode)

	// Get invite statistics from user table fields
	// Use the pre-calculated counts from the user table instead of real-time queries
	directCount := user.DirectInvitesCount
	indirectCount := user.IndirectInvitesCount
	totalCount := user.TotalInvitesCount

	g.Log().Infof(ctx, "Invite statistics for user %d: direct=%d, indirect=%d, total=%d",
		user.Id, directCount, indirectCount, totalCount)

	// Get total commission
	g.Log().Infof(ctx, "Getting commission for user ID %d (Telegram ID: %d)", user.Id, query.From.ID)
	totalCommissionAmount, err := service.User().GetTotalCommissionAmount(ctx, user.Id)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get total commission: %v", err)
		totalCommissionAmount = decimal.Zero
	}
	totalCommission, _ := totalCommissionAmount.Float64()
	g.Log().Infof(ctx, "Total commission amount: %s, float64: %f", totalCommissionAmount.String(), totalCommission)

	// Get reward percentages from configuration
	directRewardAmount, err := config.GetFloat64(ctx, "invite.direct_reward_amount", 1.0)
	if err != nil {
		g.Log().Warningf(ctx, "Failed to get direct reward amount: %v, using default 1.0", err)
		directRewardAmount = 1.0
	}

	indirectRewardAmount, err := config.GetFloat64(ctx, "invite.indirect_reward_amount", 1.5)
	if err != nil {
		g.Log().Warningf(ctx, "Failed to get indirect reward amount: %v, using default 1.5", err)
		indirectRewardAmount = 1.5
	}

	// Format percentages as strings without % sign
	directRewardStr := fmt.Sprintf("%.1f", directRewardAmount)
	indirectRewardStr := fmt.Sprintf("%.1f", indirectRewardAmount)

	// Format message with detailed invite statistics
	msgText := fmt.Sprintf("%s\n\n%s\n<code>%s</code>\n\n%s\n%s\n%s\n%s\n\n%s\n%s\n%s",
		i18n.T(ctx, "{#InviteFriendsTitle}"),
		i18n.T(ctx, "{#InviteLinkLabel}"),
		inviteLink,
		i18n.T(ctx, "{#InviteRewardsTitle}"),
		i18n.Tf(ctx, "{#DirectRewardText}", directRewardStr),
		i18n.Tf(ctx, "{#IndirectRewardText}", indirectRewardStr),
		i18n.T(ctx, "{#RewardUnlimitedText}"),
		i18n.T(ctx, "{#InviteStatsTitle}"),
		i18n.Tf(ctx, "{#InvitedUsersCount}", totalCount), // 显示总邀请人数（直推+间推）
		i18n.Tf(ctx, "{#TotalCommission}", totalCommission),
	)

	// Create keyboard with additional button for viewing subordinates
	keyboard := tgbotapi.NewInlineKeyboardMarkup(

		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "{#ViewDirectButton}"), "direct_subordinates"),
			tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "{#ViewIndirectButton}"), "view_indirect_referrals"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔙 "+i18n.T(ctx, "{#BackButton}"), "back_to_start"),
		),
	)

	if query.Message == nil {
		return nil, fmt.Errorf("no message in callback query")
	}

	// Use MediaResponseBuilder to handle system media consistently with games and main menu
	builder := shared.NewMediaResponseBuilder()
	return builder.BuildSystemMediaResponse(ctx, query, msgText, &keyboard, true), nil
}

func HandleDirectSubordinatesMenu(ctx context.Context, query *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	userID := query.From.ID
	return showSubordinatesPage(ctx, query, userID, 1)
}

func HandleSubordinatePage(ctx context.Context, query *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	userID := query.From.ID
	callbackData := query.Data
	pageStr := gstr.Replace(callbackData, "subordinate_page_", "", 1)
	page := max(g.NewVar(pageStr).Int(), 1)

	return showSubordinatesPage(ctx, query, userID, page)
}

func showSubordinatesPage(ctx context.Context, query *tgbotapi.CallbackQuery, userID int64, page int) (callback.CallbackResponse, error) {
	i18n := service.I18n().Instance()
	var subordinates []entity.Users
	offset := (page - 1) * SubordinatesPerPage

	// Get user from Telegram ID
	user, err := service.User().GetUserByTelegramId(ctx, userID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get user by telegram ID %d: %v", userID, err)
		builder := shared.NewMediaResponseBuilder()
		return builder.BuildSystemMediaResponse(ctx, query, i18n.T(ctx, "{#SystemError}"), nil, true), nil
	}

	err = dao.Users.Ctx(ctx).
		Where(dao.Users.Columns().RecommendId, user.Id).
		Order("created_at DESC").
		Limit(SubordinatesPerPage).
		Offset(offset).
		Scan(&subordinates)

	if err != nil {
		g.Log().Error(ctx, "Failed to query subordinates:", err)
		builder := shared.NewMediaResponseBuilder()
		return builder.BuildSystemMediaResponse(ctx, query, i18n.T(ctx, "{#SystemError}"), nil, true), nil
	}

	totalCount, err := dao.Users.Ctx(ctx).
		Where(dao.Users.Columns().RecommendId, user.Id).
		Count()
	if err != nil {
		g.Log().Error(ctx, "Failed to count subordinates:", err)
		return nil, err
	}

	totalPages := (totalCount + SubordinatesPerPage - 1) / SubordinatesPerPage

	var text string
	if len(subordinates) == 0 && page == 1 {
		text = i18n.T(ctx, "{#NoSubordinatesMessage}")
	} else {
		text = i18n.T(ctx, "{#DirectSubordinatesTitle}") + "\n\n"

		for i := range subordinates {
			num := offset + i + 1

			// Get user's display name from backup accounts
			firstName, err := service.User().GetUserDisplayName(ctx, subordinates[i].Id)
			if err != nil || firstName == "" {
				firstName = i18n.T(ctx, "{#UnknownUser}")
			}

			// Get telegram ID from backup accounts
			backupAccount, err := dao.UserBackupAccounts.Ctx(ctx).
				Where(dao.UserBackupAccounts.Columns().UserId, subordinates[i].Id).
				Where(dao.UserBackupAccounts.Columns().IsMaster, 1).
				One()

			var telegramId int64
			if err == nil && !backupAccount.IsEmpty() {
				telegramId = backupAccount[dao.UserBackupAccounts.Columns().TelegramId].Int64()
			}

			createdAt := subordinates[i].CreatedAt.Format("Y-m-d")
			text += fmt.Sprintf("%d. %s (ID: %d)\n%s: %s\n\n",
				num, firstName, telegramId,
				i18n.T(ctx, "{#RegistrationTime}"), createdAt)
		}
	}

	var rows [][]tgbotapi.InlineKeyboardButton

	if len(subordinates) > 0 {
		var detailButtons []tgbotapi.InlineKeyboardButton
		for i := range subordinates {
			num := offset + i + 1
			detailButtons = append(detailButtons,
				tgbotapi.NewInlineKeyboardButtonData(
					fmt.Sprintf("%s %d %s",
						i18n.T(ctx, "{#DetailPrefix}"),
						num,
						i18n.T(ctx, "{#DetailSuffix}")),
					fmt.Sprintf("subordinate_detail_%d", subordinates[i].Id),
				))

			if (i+1)%2 == 0 || i == len(subordinates)-1 {
				rows = append(rows, detailButtons)
				detailButtons = []tgbotapi.InlineKeyboardButton{}
			}
		}
	}

	if totalPages > 1 {
		var paginationButtons []tgbotapi.InlineKeyboardButton

		if page > 1 {
			paginationButtons = append(paginationButtons,
				tgbotapi.NewInlineKeyboardButtonData("⬅️",
					fmt.Sprintf("subordinate_page_%d", page-1)))
		}

		if page < totalPages {
			paginationButtons = append(paginationButtons,
				tgbotapi.NewInlineKeyboardButtonData("➡️",
					fmt.Sprintf("subordinate_page_%d", page+1)))
		}

		if len(paginationButtons) > 0 {
			rows = append(rows, paginationButtons)
		}
	}

	rows = append(rows, []tgbotapi.InlineKeyboardButton{
		tgbotapi.NewInlineKeyboardButtonData(
			"🔙 "+i18n.T(ctx, "{#BackButton}"),
			"invite_friends",
		),
	})

	keyboard := tgbotapi.NewInlineKeyboardMarkup(rows...)

	// Use MediaResponseBuilder to handle system media consistently
	builder := shared.NewMediaResponseBuilder()
	return builder.BuildSystemMediaResponse(ctx, query, text, &keyboard, true), nil
}

func HandleSubordinateDetail(ctx context.Context, query *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	i18n := service.I18n().Instance()

	callbackData := query.Data
	subordinateIDStr := gstr.Replace(callbackData, "subordinate_detail_", "", 1)
	subordinateID := g.NewVar(subordinateIDStr).Int64()

	var subordinate entity.Users
	err := dao.Users.Ctx(ctx).
		Where(dao.Users.Columns().Id, subordinateID).
		Scan(&subordinate)

	if err != nil {
		g.Log().Error(ctx, "Failed to query subordinate detail:", err)
		builder := shared.NewMediaResponseBuilder()
		return builder.BuildSystemMediaResponse(ctx, query, i18n.T(ctx, "{#SystemError}"), nil, true), nil
	}

	// Get user's display name and username from backup accounts
	backupAccount, err := dao.UserBackupAccounts.Ctx(ctx).
		Where(dao.UserBackupAccounts.Columns().UserId, subordinate.Id).
		Where(dao.UserBackupAccounts.Columns().IsMaster, 1).
		One()

	var firstName, username string
	if err == nil && !backupAccount.IsEmpty() {
		firstName = backupAccount[dao.UserBackupAccounts.Columns().FirstName].String()
		username = backupAccount[dao.UserBackupAccounts.Columns().TelegramUsername].String()
		if firstName == "" {
			firstName = i18n.T(ctx, "{#UnknownUser}")
		}
	} else {
		firstName = i18n.T(ctx, "{#UnknownUser}")
	}

	// Format username display
	usernameDisplay := ""
	if username != "" {
		usernameDisplay = " " + username
	}

	// Get direct reward rate from config
	directRewardRate, err := config.GetFloat64(ctx, "invite.direct_reward_amount", 1.0)
	if err != nil {
		g.Log().Warningf(ctx, "Failed to get direct reward amount: %v, using default 1.0", err)
		directRewardRate = 1.0
	}

	// Get financial statistics from users table
	financialStats, err := service.User().GetUserFinancialStats(ctx, subordinate.Id)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get subordinate financial stats: %v", err)
		// Fallback to zero values
		financialStats = &service.UserFinancialStats{
			TotalDeposits:    decimal.Zero,
			TotalWithdrawals: decimal.Zero,
			TotalCommission:  decimal.Zero,
		}
	}

	depositAmount := formatAmount(financialStats.TotalDeposits)
	withdrawAmount := formatAmount(financialStats.TotalWithdrawals)

	// Get current user to calculate commission generated FOR current user BY this subordinate
	currentUser, err := service.User().GetUserByTelegramId(ctx, query.From.ID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get current user: %v", err)
		builder := shared.NewMediaResponseBuilder()
		return builder.BuildSystemMediaResponse(ctx, query, i18n.T(ctx, "{#SystemError}"), nil, true), nil
	}

	// Get commission generated by this subordinate FOR the current user
	commissionGenerated, err := service.User().GetCommissionGeneratedByUser(ctx, subordinate.Id, currentUser.Id)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get commission generated by subordinate: %v", err)
		commissionGenerated = decimal.Zero
	}
	commissionAmount := formatAmount(commissionGenerated)

	// Build message text with the new format
	text := fmt.Sprintf(
		"%s: %s%s\n\n"+
			"%s\n"+
			"%s: %s %s: %s\n\n"+
			"%s\n"+
			"- %s",
		i18n.T(ctx, "{#SubordinateDetailTitle}"), firstName, usernameDisplay,
		i18n.T(ctx, "{#MonthlyStatisticsTitle}"),
		i18n.T(ctx, "{#TotalDeposits}"), depositAmount,
		i18n.T(ctx, "{#TotalWithdrawals}"), withdrawAmount,
		i18n.T(ctx, "{#CommissionEarningsTitle}"),
		i18n.Tf(ctx, "{#CommissionEarnedWithRate}", commissionAmount, directRewardRate),
	)

	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"🔙 "+i18n.T(ctx, "{#BackButton}"),
				"direct_subordinates",
			),
		),
	)

	// Use MediaResponseBuilder to handle system media consistently
	builder := shared.NewMediaResponseBuilder()
	return builder.BuildSystemMediaResponse(ctx, query, text, &keyboard, true), nil
}

func HandleIndirectSubordinatesMenu(ctx context.Context, query *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	userID := query.From.ID
	return showIndirectSubordinatesPage(ctx, query, userID, 1)
}

func HandleIndirectSubordinatePage(ctx context.Context, query *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	userID := query.From.ID
	callbackData := query.Data
	pageStr := gstr.Replace(callbackData, "indirect_subordinate_page_", "", 1)
	page := max(g.NewVar(pageStr).Int(), 1)

	return showIndirectSubordinatesPage(ctx, query, userID, page)
}

func showIndirectSubordinatesPage(ctx context.Context, query *tgbotapi.CallbackQuery, userID int64, page int) (callback.CallbackResponse, error) {
	i18n := service.I18n().Instance()
	var indirectSubordinates []entity.Users
	offset := (page - 1) * SubordinatesPerPage

	// Get user from Telegram ID
	user, err := service.User().GetUserByTelegramId(ctx, userID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get user by telegram ID %d: %v", userID, err)
		builder := shared.NewMediaResponseBuilder()
		return builder.BuildSystemMediaResponse(ctx, query, i18n.T(ctx, "{#SystemError}"), nil, true), nil
	}

	// First get all direct subordinates
	var directSubordinates []entity.Users
	err = dao.Users.Ctx(ctx).
		Where(dao.Users.Columns().RecommendId, user.Id).
		Scan(&directSubordinates)

	if err != nil {
		g.Log().Error(ctx, "Failed to query direct subordinates:", err)
		builder := shared.NewMediaResponseBuilder()
		return builder.BuildSystemMediaResponse(ctx, query, i18n.T(ctx, "{#SystemError}"), nil, true), nil
	}

	// Extract IDs from direct subordinates
	var directSubordinateIDs []uint64
	for i := range directSubordinates {
		directSubordinateIDs = append(directSubordinateIDs, directSubordinates[i].Id)
	}

	// If no direct subordinates, no indirect subordinates possible
	if len(directSubordinateIDs) == 0 {
		text := i18n.T(ctx, "{#NoIndirectSubordinatesMessage}")
		keyboard := tgbotapi.NewInlineKeyboardMarkup(
			[]tgbotapi.InlineKeyboardButton{
				tgbotapi.NewInlineKeyboardButtonData(
					"🔙 "+i18n.T(ctx, "{#BackButton}"),
					"invite_friends",
				),
			})

		builder := shared.NewMediaResponseBuilder()
		return builder.BuildSystemMediaResponse(ctx, query, text, &keyboard, true), nil
	}

	// Get indirect subordinates (users referred by direct subordinates)
	err = dao.Users.Ctx(ctx).
		WhereIn(dao.Users.Columns().RecommendId, directSubordinateIDs).
		Where(dao.Users.Columns().RecommendId+" != ?", 0). // Exclude users with no referrer
		Order("created_at DESC").
		Limit(SubordinatesPerPage).
		Offset(offset).
		Scan(&indirectSubordinates)

	if err != nil {
		g.Log().Error(ctx, "Failed to query indirect subordinates:", err)
		builder := shared.NewMediaResponseBuilder()
		return builder.BuildSystemMediaResponse(ctx, query, i18n.T(ctx, "{#SystemError}"), nil, true), nil
	}

	totalCount, err := dao.Users.Ctx(ctx).
		WhereIn(dao.Users.Columns().RecommendId, directSubordinateIDs).
		Where(dao.Users.Columns().RecommendId+" != ?", 0). // Exclude users with no referrer
		Count()
	if err != nil {
		g.Log().Error(ctx, "Failed to count indirect subordinates:", err)
		return nil, err
	}

	totalPages := (totalCount + SubordinatesPerPage - 1) / SubordinatesPerPage

	var text string
	if len(indirectSubordinates) == 0 && page == 1 {
		text = i18n.T(ctx, "{#NoIndirectSubordinatesMessage}")
	} else {
		text = i18n.T(ctx, "{#IndirectSubordinatesTitle}") + "\n\n"

		for i := range indirectSubordinates {
			num := offset + i + 1

			// Get user's display name from backup accounts
			firstName, err := service.User().GetUserDisplayName(ctx, indirectSubordinates[i].Id)
			if err != nil || firstName == "" {
				firstName = i18n.T(ctx, "{#UnknownUser}")
			}

			// Get telegram ID from backup accounts
			backupAccount, err := dao.UserBackupAccounts.Ctx(ctx).
				Where(dao.UserBackupAccounts.Columns().UserId, indirectSubordinates[i].Id).
				Where(dao.UserBackupAccounts.Columns().IsMaster, 1).
				One()

			var telegramId string
			if err == nil && !backupAccount.IsEmpty() {
				telegramId = backupAccount[dao.UserBackupAccounts.Columns().TelegramId].String()
			}

			// Get referrer's name (the direct subordinate who referred this user)
			var referrerName string
			if indirectSubordinates[i].RecommendId > 0 {
				referrerName, err = service.User().GetUserDisplayName(ctx, indirectSubordinates[i].RecommendId)
				if err != nil || referrerName == "" {
					referrerName = fmt.Sprintf("User %d", indirectSubordinates[i].RecommendId)
				}
			} else {
				referrerName = i18n.T(ctx, "{#UnknownUser}")
			}

			createdAt := indirectSubordinates[i].CreatedAt.Format("Y-m-d")
			text += fmt.Sprintf("%d. %s (ID:%s)\n%s: %s\n%s: %s\n\n",
				num, firstName, telegramId,
				i18n.T(ctx, "{#ReferredBy}"), referrerName,
				i18n.T(ctx, "{#RegistrationTime}"), createdAt)
		}
	}

	var rows [][]tgbotapi.InlineKeyboardButton

	if len(indirectSubordinates) > 0 {
		var detailButtons []tgbotapi.InlineKeyboardButton
		for i := range indirectSubordinates {
			num := offset + i + 1
			detailButtons = append(detailButtons,
				tgbotapi.NewInlineKeyboardButtonData(
					fmt.Sprintf("%s %d %s",
						i18n.T(ctx, "{#DetailPrefix}"),
						num,
						i18n.T(ctx, "{#DetailSuffix}")),
					fmt.Sprintf("indirect_subordinate_detail_%d", indirectSubordinates[i].Id),
				))

			if (i+1)%2 == 0 || i == len(indirectSubordinates)-1 {
				rows = append(rows, detailButtons)
				detailButtons = []tgbotapi.InlineKeyboardButton{}
			}
		}
	}

	if totalPages > 1 {
		var paginationButtons []tgbotapi.InlineKeyboardButton

		if page > 1 {
			paginationButtons = append(paginationButtons,
				tgbotapi.NewInlineKeyboardButtonData("⬅️",
					fmt.Sprintf("indirect_subordinate_page_%d", page-1)))
		}

		if page < totalPages {
			paginationButtons = append(paginationButtons,
				tgbotapi.NewInlineKeyboardButtonData("➡️",
					fmt.Sprintf("indirect_subordinate_page_%d", page+1)))
		}

		if len(paginationButtons) > 0 {
			rows = append(rows, paginationButtons)
		}
	}

	rows = append(rows, []tgbotapi.InlineKeyboardButton{
		tgbotapi.NewInlineKeyboardButtonData(
			"🔙 "+i18n.T(ctx, "{#BackButton}"),
			"invite_friends",
		),
	})

	keyboard := tgbotapi.NewInlineKeyboardMarkup(rows...)

	// Use MediaResponseBuilder to handle system media consistently
	builder := shared.NewMediaResponseBuilder()
	return builder.BuildSystemMediaResponse(ctx, query, text, &keyboard, true), nil
}

func HandleIndirectSubordinateDetail(ctx context.Context, query *tgbotapi.CallbackQuery) (callback.CallbackResponse, error) {
	i18n := service.I18n().Instance()

	callbackData := query.Data
	subordinateIDStr := gstr.Replace(callbackData, "indirect_subordinate_detail_", "", 1)
	subordinateID := g.NewVar(subordinateIDStr).Int64()

	var subordinate entity.Users
	err := dao.Users.Ctx(ctx).
		Where(dao.Users.Columns().Id, subordinateID).
		Scan(&subordinate)

	if err != nil {
		g.Log().Error(ctx, "Failed to query indirect subordinate detail:", err)
		builder := shared.NewMediaResponseBuilder()
		return builder.BuildSystemMediaResponse(ctx, query, i18n.T(ctx, "{#SystemError}"), nil, true), nil
	}

	// Get user's display name and username from backup accounts
	backupAccount, err := dao.UserBackupAccounts.Ctx(ctx).
		Where(dao.UserBackupAccounts.Columns().UserId, subordinate.Id).
		Where(dao.UserBackupAccounts.Columns().IsMaster, 1).
		One()

	var firstName, username string
	if err == nil && !backupAccount.IsEmpty() {
		firstName = backupAccount[dao.UserBackupAccounts.Columns().FirstName].String()
		username = backupAccount[dao.UserBackupAccounts.Columns().TelegramUsername].String()
		if firstName == "" {
			firstName = i18n.T(ctx, "{#UnknownUser}")
		}
	} else {
		firstName = i18n.T(ctx, "{#UnknownUser}")
	}

	// Format username display
	usernameDisplay := ""
	if username != "" {
		usernameDisplay = " " + username
	}

	// Get referrer information
	var referrerName string
	if subordinate.RecommendId > 0 {
		referrerName, err = service.User().GetUserDisplayName(ctx, subordinate.RecommendId)
		if err != nil || referrerName == "" {
			referrerName = fmt.Sprintf("User %d", subordinate.RecommendId)
		}
	} else {
		referrerName = i18n.T(ctx, "{#UnknownUser}")
	}

	// Get financial statistics from users table
	financialStats, err := service.User().GetUserFinancialStats(ctx, subordinate.Id)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get subordinate financial stats: %v", err)
		// Fallback to zero values
		financialStats = &service.UserFinancialStats{
			TotalDeposits:    decimal.Zero,
			TotalWithdrawals: decimal.Zero,
			TotalCommission:  decimal.Zero,
		}
	}

	depositAmount := formatAmount(financialStats.TotalDeposits)
	withdrawAmount := formatAmount(financialStats.TotalWithdrawals)

	// Get current user to calculate commission generated FOR current user BY this subordinate
	currentUser, err := service.User().GetUserByTelegramId(ctx, query.From.ID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get current user: %v", err)
		builder := shared.NewMediaResponseBuilder()
		return builder.BuildSystemMediaResponse(ctx, query, i18n.T(ctx, "{#SystemError}"), nil, true), nil
	}

	// Get commission generated by this subordinate FOR the current user
	commissionGenerated, err := service.User().GetCommissionGeneratedByUser(ctx, subordinate.Id, currentUser.Id)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get commission generated by subordinate: %v", err)
		commissionGenerated = decimal.Zero
	}
	commissionAmount := formatAmount(commissionGenerated)

	// Get indirect reward rate from config
	indirectRewardRate, err := config.GetFloat64(ctx, "invite.indirect_reward_amount", 1.5)
	if err != nil {
		g.Log().Warningf(ctx, "Failed to get indirect reward amount: %v, using default 1.5", err)
		indirectRewardRate = 1.5
	}

	// Build message text with the new format
	text := fmt.Sprintf(
		"%s: %s%s\n"+
			"%s: %s\n\n"+
			"%s\n"+
			"%s: %s %s: %s\n\n"+
			"%s\n"+
			"- %s",
		i18n.T(ctx, "{#IndirectSubordinateDetailTitle}"), firstName, usernameDisplay,
		i18n.T(ctx, "{#ReferredBy}"), referrerName,
		i18n.T(ctx, "{#MonthlyStatisticsTitle}"),
		i18n.T(ctx, "{#TotalDeposits}"), depositAmount,
		i18n.T(ctx, "{#TotalWithdrawals}"), withdrawAmount,
		i18n.T(ctx, "{#CommissionEarningsTitle}"),
		i18n.Tf(ctx, "{#CommissionEarnedWithRate}", commissionAmount, indirectRewardRate),
	)

	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"🔙 "+i18n.T(ctx, "{#BackButton}"),
				"view_indirect_referrals",
			),
		),
	)

	// Use MediaResponseBuilder to handle system media consistently
	builder := shared.NewMediaResponseBuilder()
	return builder.BuildSystemMediaResponse(ctx, query, text, &keyboard, true), nil
}

// formatAmount formats a decimal amount to a string with 2 decimal places
func formatAmount(amount decimal.Decimal) string {
	// If the amount is zero, return "0"
	if amount.IsZero() {
		return "0"
	}
	// Format with 2 decimal places
	return amount.StringFixed(2)
}
