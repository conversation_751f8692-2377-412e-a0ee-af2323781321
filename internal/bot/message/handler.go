package message

import (
	"context"
	"strings"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/frame/g"

	"telegram-bot-api/internal/bot/passfree" // Import passfree logic

	"github.com/a19ba14d/tg-bot-common/codes"  // Assuming sentinel error is here
	"github.com/a19ba14d/tg-bot-common/consts" // Import consts package

	"telegram-bot-api/internal/admin"         // Import admin logic for admin state handling
	"telegram-bot-api/internal/bot/deposit"   // Import deposit logic for Okpay handlers
	"telegram-bot-api/internal/bot/profile"   // Import profile logic for input handlers
	"telegram-bot-api/internal/bot/redpacket" // Import redpacket logic
	"telegram-bot-api/internal/bot/withdraw"  // Import withdraw logic
	localConsts "telegram-bot-api/internal/consts"
	"telegram-bot-api/internal/model"
	"telegram-bot-api/internal/model/user_state" // Import user_state constants
	"telegram-bot-api/internal/security"
	"telegram-bot-api/internal/service"
)

// Handler is the main entry point for processing incoming non-command text messages.
// It checks the user's current interaction state and routes the message accordingly.
func Handler(ctx context.Context, message *tgbotapi.Message) (*tgbotapi.MessageConfig, error) {
	if message == nil || message.From == nil {
		g.Log().Warning(ctx, "Received nil message or message without sender, skipping.")
		return nil, nil // Ignore invalid messages
	}
	// Removed check for empty message.Text to allow processing non-text messages like photos.
	// if message.Text == "" {
	// 	return nil, nil // Ignore empty messages in this handler
	// }

	telegramID := message.From.ID
	g.Log().Debugf(ctx, "Processing text message from user %d: %s", telegramID, message.Text)

	// Security check for text messages
	if message.Text != "" {
		// Check rate limit first
		monitor := security.GetMonitor()
		allowed, errMsg := monitor.CheckRateLimit(ctx, telegramID)
		if !allowed {
			g.Log().Warningf(ctx, "User %d rate limited: %s", telegramID, errMsg)
			resp := tgbotapi.NewMessage(message.Chat.ID, errMsg)
			return &resp, nil
		}

		// Validate input for general security threats
		validator := security.GetValidator()
		validationResult := validator.ValidateInput(ctx, message.Text, security.InputTypeGeneral)
		if !validationResult.IsValid {
			// Log security event
			g.Log().Warningf(ctx, "Security validation failed for user %d: %s", telegramID, validationResult.Details)

			// Record serious security events
			if validationResult.Details == "SQLi detected" || validationResult.Details == "XSS detected" {
				monitor.RecordSecurityEvent(ctx, telegramID, security.EventSQLiDetected, validationResult.Details)
			}

			// Send user-friendly error message
			i18n := service.I18n()
			errorText := i18n.T(ctx, validationResult.ErrorKey)
			if errorText == validationResult.ErrorKey {
				// Fallback if i18n key not found
				errorText = validationResult.ErrorMessage
			}
			resp := tgbotapi.NewMessage(message.Chat.ID, errorText)
			return &resp, nil
		}
	}

	// Check for group red packet commands before processing user state
	if message.Chat != nil && message.Chat.ID < 0 && message.Text != "" {
		// This is a group message, check for red packet commands
		text := strings.TrimSpace(message.Text)
		if strings.HasPrefix(text, "hb ") || strings.HasPrefix(text, "xyhb ") {
			g.Log().Infof(ctx, "Detected group red packet command from user %d in chat %d: %s", telegramID, message.Chat.ID, text)
			// Route to group red packet handler
			handled, err := handleGroupRedPacketCommand(ctx, message)
			if err != nil {
				g.Log().Errorf(ctx, "Error handling group red packet command for user %d: %v", telegramID, err)
				return nil, err
			}
			if handled {
				g.Log().Debugf(ctx, "Group red packet command handled for user %d", telegramID)
				return nil, codes.ErrMessageHandled
			}
		}
	}

	// 1. Get User State
	userState, err := service.UserState().GetUserStateByTelegramId(ctx, telegramID)
	if err != nil {
		// Log the error but potentially allow fallback or inform the user
		g.Log().Errorf(ctx, "Error getting user state for user %d: %v", telegramID, err)
		// Depending on the error type, might send a generic error message
		// For now, let's just return nil to avoid blocking other potential handlers
		return nil, nil
	}

	// 2. Check if a specific interaction is expected
	if userState == nil || userState.State == model.StateIdle {
		g.Log().Debugf(ctx, "User %d is in IDLE state or state is nil. No specific input expected.", telegramID)
		// No specific state, this message might be a command or casual text.
		// Let other handlers (e.g., command handler) process it.
		// If no other handler processes it, it will be ignored.
		return nil, nil
	}

	// 3. State exists - Process based on the expected interaction
	g.Log().Infof(ctx, "User %d is in state '%s'. Processing input: %s", telegramID, userState.State, message.Text)

	// 4. Route to Specific Module Handler based on State
	g.Log().Debugf(ctx, "[MESSAGE_HANDLER_DEBUG] Reached state switch for user %d in state '%s'. Message Text: '%s', Has Photo: %v", telegramID, userState.State, message.Text, message.Photo != nil)
	switch userState.State {
	case withdraw.WithdrawStateName:
		// Delegate to withdraw message handler
		// Input validation should happen inside HandleWithdrawMessage
		handled, err := withdraw.HandleWithdrawMessage(ctx, message)
		if err != nil {
			// Error occurred within the handler
			g.Log().Errorf(ctx, "Error from withdraw message handler for user %d: %v", telegramID, err)
			// Return nil response, but propagate the error to the registry/caller
			return nil, err
		}
		if handled {
			// Withdraw handler successfully processed the message (sent reply or handled error internally)
			g.Log().Debugf(ctx, "Message from user %d handled by withdraw state handler.", telegramID)
			// Return sentinel error to indicate handled and stop further handlers in the registry
			return nil, codes.ErrMessageHandled
		}
		// If not handled by withdraw state (e.g., unexpected step), return nil, nil to allow fallthrough
		g.Log().Debugf(ctx, "Message from user %d in withdraw state was not handled by withdraw handler (state step: %s). Allowing fallthrough.", telegramID, userState.Context["step"]) // Assuming step is in context map
		return nil, nil
	case model.StateAwaitingGoogle2FACode, model.StateAwaitingPaymentPassword, model.StateWaitingBackupVerificationPassword:
		// Route to Profile Input Handler (assuming profile handles these)
		// TODO: Consider moving input validation inside the specific handlers too.
		if !profile.ValidateInput(message.Text, userState.ExpectedInputType) {
			g.Log().Debugf(ctx, "Invalid input format from user %d for state %s. Expected: %s, Got: %s",
				telegramID, userState.State, userState.ExpectedInputType, message.Text)
			i18n := service.I18n() // Use service directly
			errorText := i18n.T(ctx, "{#InputFormatError}")
			if userState.ExpectedInputType == model.InputTypeNumeric6Digits {
				errorText = i18n.T(ctx, "{#Google2FACodeFormatError}")
			}
			errMsg := tgbotapi.NewMessage(message.Chat.ID, errorText)
			errMsg.ReplyToMessageID = message.MessageID
			return &errMsg, nil
		}
		handlerFunc := profile.GetInputHandler(userState.State)
		if handlerFunc != nil {
			return handlerFunc(ctx, userState, message)
		} else {
			g.Log().Warningf(ctx, "User %d is in profile state '%s' but no input handler is registered. Clearing state.", telegramID, userState.State)
			_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
			return nil, nil // Ignore
		}
		// Note: The original code had 'return nil, nil' here, which seems incorrect
		// as it would prevent the default case from being reached if the handlerFunc was nil.
		// Assuming the logic intended to fall through or return handled if handlerFunc was executed.
		// Let's keep the fallthrough behaviour for now by removing the redundant return.
	case string(user_state.StatePassFreeAmountInput):
		// Delegate to passfree message handler
		handled, err := passfree.HandlePassFreeAmountInput(ctx, message)
		if err != nil {
			g.Log().Errorf(ctx, "Error from passfree message handler for user %d: %v", telegramID, err)
			return nil, err // Propagate error
		}
		if handled {
			g.Log().Debugf(ctx, "Message from user %d handled by passfree state handler.", telegramID)
			return nil, codes.ErrMessageHandled // Stop further processing
		}
		// If not handled (should not happen with current implementation)
		g.Log().Warningf(ctx, "Message from user %d in passfree state was not handled by passfree handler.", telegramID)
		return nil, nil // Allow fallthrough? Or treat as unhandled? Let's fallthrough for now.

	case consts.UserStateWaitingRedPacketQuantity, consts.UserStateWaitingRedPacketAmount, consts.UserStateWaitingRedPacketBlessing, consts.UserStateWaitingRedPacketCover, localConsts.UserStateWaitingRedPacketPaymentConfirm: // Add cover state and payment confirm state
		// Delegate to redpacket message handler (which handles both text and photo based on state)
		handled, err := redpacket.HandleRedPacketMessage(ctx, message)
		if err != nil {
			g.Log().Errorf(ctx, "Error from redpacket message handler for user %d: %v", telegramID, err)
			return nil, err // Propagate error
		}
		if handled {
			g.Log().Debugf(ctx, "Message from user %d handled by redpacket state handler.", telegramID)
			return nil, codes.ErrMessageHandled // Stop further processing
		}
		// If not handled (should ideally not happen if state matches)
		g.Log().Warningf(ctx, "Message from user %d in redpacket state '%s' was not handled.", telegramID, userState.State)
		// Clear state for safety if not handled?
		_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
		return nil, nil

	case deposit.StateOkpayDepositAwaitingAmount:
		// Delegate to Okpay deposit amount input handler
		handled, err := deposit.HandleOkpayAmountInput(ctx, message)
		if err != nil {
			g.Log().Errorf(ctx, "Error from Okpay deposit message handler for user %d: %v", telegramID, err)
			return nil, err
		}
		if handled {
			g.Log().Debugf(ctx, "Message from user %d handled by Okpay deposit handler.", telegramID)
			return nil, codes.ErrMessageHandled
		}
		return nil, nil

	default:
		// Check if this is an admin state that should be handled by the admin module
		if strings.HasPrefix(userState.State, "admin_") || userState.State == "AWAITING_DAILY_REPORT_DATE_RANGE" {
			g.Log().Debugf(ctx, "User %d is in admin state '%s'. Delegating to admin handler.", telegramID, userState.State)
			// Import admin package to access HandleMessage
			// Note: We need to import the admin package at the top of this file
			handled, err := handleAdminState(ctx, message)
			if err != nil {
				g.Log().Errorf(ctx, "Error from admin message handler for user %d: %v", telegramID, err)
				return nil, err
			}
			if handled {
				g.Log().Debugf(ctx, "Message from user %d handled by admin handler.", telegramID)
				return nil, codes.ErrMessageHandled
			}
			// If not handled by admin handler, fall through to default behavior
		}

		// State exists but no handler is registered for it
		g.Log().Warningf(ctx, "User %d is in unhandled state '%s'. Clearing state.", telegramID, userState.State)
		_ = service.UserState().ClearUserStateByTelegramId(ctx, telegramID)
		// Optionally inform the user about the unexpected state
		// i18n := service.I18n()
		// errMsg := tgbotapi.NewMessage(message.Chat.ID, i18n.T(ctx, "{#SystemError}"))
		// return &errMsg, nil
		return nil, nil // Ignore for now
	}

	// Fallback if switch doesn't handle the state
	// return nil, nil
}

// Note: validateInput moved to profile/input_handlers.go

// handleGroupRedPacketCommand is implemented in group_redpacket.go

// handleAdminState delegates admin state handling to the admin module
func handleAdminState(ctx context.Context, message *tgbotapi.Message) (bool, error) {
	// Delegate to admin.HandleMessage
	return admin.HandleMessage(ctx, message)
}

// TODO: Implement specific input processing functions (Step 3 onwards)
// func processGoogle2FACode(ctx context.Context, state *model.UserState, message *tgbotapi.Message) (*tgbotapi.MessageConfig, error) { ... }
// func processPaymentPassword(ctx context.Context, state *model.UserState, message *tgbotapi.Message) (*tgbotapi.MessageConfig, error) { ... }
