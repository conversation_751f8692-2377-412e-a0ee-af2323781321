package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"

	"telegram-bot-api/internal/dao"
	"telegram-bot-api/internal/tenant"
)

// DailyReportResult represents the daily report query result
type DailyReportResult struct {
	Items      []DailyReportItem `json:"items"`
	TotalCount int64             `json:"total_count"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	StartDate  *gtime.Time       `json:"start_date"`
	EndDate    *gtime.Time       `json:"end_date"`
}

// DailyReportItem represents a single day's report data
type DailyReportItem struct {
	Date             string          `json:"date"`
	DepositCount     int64           `json:"deposit_count"`
	DepositAmount    decimal.Decimal `json:"deposit_amount"`
	WithdrawalCount  int64           `json:"withdrawal_count"`
	WithdrawalAmount decimal.Decimal `json:"withdrawal_amount"`
	NetAmount        decimal.Decimal `json:"net_amount"`
}

// sDailyReport implements the daily report service
type sDailyReport struct{}

var dailyReportService = &sDailyReport{}

// DailyReportService returns the daily report service instance
func DailyReportService() *sDailyReport {
	return dailyReportService
}

// ValidateDateRange validates the date range input in YYYY-MM-DD format
func (s *sDailyReport) ValidateDateRange(startDate, endDate string) (*gtime.Time, *gtime.Time, error) {
	// First validate the format using regex or manual check
	if !s.isValidDateFormat(startDate) {
		return nil, nil, gerror.New("invalid start date format, please use YYYY-MM-DD")
	}
	if !s.isValidDateFormat(endDate) {
		return nil, nil, gerror.New("invalid end date format, please use YYYY-MM-DD")
	}

	// Parse start date
	start, err := gtime.StrToTime(startDate+" 00:00:00", "Y-m-d H:i:s")
	if err != nil {
		return nil, nil, gerror.New("invalid start date: " + err.Error())
	}

	// Parse end date
	end, err := gtime.StrToTime(endDate+" 23:59:59", "Y-m-d H:i:s")
	if err != nil {
		return nil, nil, gerror.New("invalid end date: " + err.Error())
	}

	// Validate date range
	if start.After(end) {
		return nil, nil, gerror.New("start date cannot be after end date")
	}

	// Check if dates are in the future
	now := gtime.Now()
	if start.After(now) {
		return nil, nil, gerror.New("start date cannot be in the future")
	}
	if end.After(now) {
		// Adjust end date to today if it's in the future
		end = gtime.New(now.Format("Y-m-d 23:59:59"))
	}

	// Check minimum date (e.g., not before 2020)
	minDate := gtime.New("2020-01-01 00:00:00")
	if start.Before(minDate) {
		return nil, nil, gerror.New("start date cannot be before 2020-01-01")
	}

	return start, end, nil
}

// isValidDateFormat checks if the date string matches YYYY-MM-DD format
func (_ *sDailyReport) isValidDateFormat(dateStr string) bool {
	if len(dateStr) != 10 {
		return false
	}

	// Check format: YYYY-MM-DD
	if dateStr[4] != '-' || dateStr[7] != '-' {
		return false
	}

	// Extract parts
	yearStr := dateStr[0:4]
	monthStr := dateStr[5:7]
	dayStr := dateStr[8:10]

	// Check if all parts are numeric
	for _, r := range yearStr + monthStr + dayStr {
		if r < '0' || r > '9' {
			return false
		}
	}

	// Parse to validate actual date values
	_, err := time.Parse("2006-01-02", dateStr)
	return err == nil
}

// GetDailyDepositWithdrawalReport gets daily aggregated deposit/withdrawal report
func (s *sDailyReport) GetDailyDepositWithdrawalReport(ctx context.Context, startDate, endDate *gtime.Time, page, pageSize int) (*DailyReportResult, error) {
	// Get tenant ID from context
	tenantId, ok := tenant.GetTenantIdFromContext(ctx)
	if !ok {
		return nil, gerror.New("tenant ID not found in context")
	}

	// Validate pagination
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 5 // Default 5 records per page
	}

	// Query deposits from user_recharges
	depositQuery := dao.UserRecharges.Ctx(ctx).
		Where("tenant_id = ?", tenantId).
		Where("created_at BETWEEN ? AND ?", startDate, endDate).
		Where("state = ?", 2). // 2-已完成/已入账(Completed)
		Fields(`
			DATE(created_at) as date,
			COUNT(*) as count,
			COALESCE(SUM(converted_amount), 0) as total_amount
		`).
		Group("DATE(created_at)")

	depositRows, err := depositQuery.All()
	if err != nil {
		return nil, gerror.Wrap(err, "failed to get deposit data")
	}

	// Query withdrawals from user_withdraws
	withdrawalQuery := dao.UserWithdraws.Ctx(ctx).
		Where("tenant_id = ?", tenantId).
		Where("created_at BETWEEN ? AND ?", startDate, endDate).
		Where("state = ?", 4). // 4-已完成(Completed)
		Fields(`
			DATE(created_at) as date,
			COUNT(*) as count,
			COALESCE(SUM(converted_amount), 0) as total_amount
		`).
		Group("DATE(created_at)")

	withdrawalRows, err := withdrawalQuery.All()
	if err != nil {
		return nil, gerror.Wrap(err, "failed to get withdrawal data")
	}

	// Process results into a map for easier aggregation
	dailyData := make(map[string]*DailyReportItem)

	// Process deposits
	for _, row := range depositRows {
		dateStr := row["date"].String()
		count := row["count"].Int64()
		amount, _ := decimal.NewFromString(row["total_amount"].String())

		if _, exists := dailyData[dateStr]; !exists {
			dailyData[dateStr] = &DailyReportItem{
				Date:             dateStr,
				DepositCount:     0,
				DepositAmount:    decimal.Zero,
				WithdrawalCount:  0,
				WithdrawalAmount: decimal.Zero,
				NetAmount:        decimal.Zero,
			}
		}

		dailyData[dateStr].DepositCount = count
		dailyData[dateStr].DepositAmount = amount
	}

	// Process withdrawals
	for _, row := range withdrawalRows {
		dateStr := row["date"].String()
		count := row["count"].Int64()
		amount, _ := decimal.NewFromString(row["total_amount"].String())

		if _, exists := dailyData[dateStr]; !exists {
			dailyData[dateStr] = &DailyReportItem{
				Date:             dateStr,
				DepositCount:     0,
				DepositAmount:    decimal.Zero,
				WithdrawalCount:  0,
				WithdrawalAmount: decimal.Zero,
				NetAmount:        decimal.Zero,
			}
		}

		dailyData[dateStr].WithdrawalCount = count
		dailyData[dateStr].WithdrawalAmount = amount
	}

	// Generate all dates in the range (even if no data)
	currentDate := startDate.Clone()
	for !currentDate.After(endDate) {
		dateStr := currentDate.Format("Y-m-d")
		if _, exists := dailyData[dateStr]; !exists {
			dailyData[dateStr] = &DailyReportItem{
				Date:             dateStr,
				DepositCount:     0,
				DepositAmount:    decimal.Zero,
				WithdrawalCount:  0,
				WithdrawalAmount: decimal.Zero,
				NetAmount:        decimal.Zero,
			}
		}
		currentDate = currentDate.Add(24 * time.Hour)
	}

	// Calculate net amounts and convert map to slice
	var items []DailyReportItem
	for _, item := range dailyData {
		item.NetAmount = item.DepositAmount.Sub(item.WithdrawalAmount)
		items = append(items, *item)
	}

	// Sort items by date (ascending) to match requirement
	// Sort by date string
	for i := 0; i < len(items); i++ {
		for j := i + 1; j < len(items); j++ {
			if items[i].Date > items[j].Date {
				items[i], items[j] = items[j], items[i]
			}
		}
	}

	totalCount := int64(len(items))

	// Apply pagination
	offset := (page - 1) * pageSize
	start := offset
	end := offset + pageSize
	if start > len(items) {
		start = len(items)
	}
	if end > len(items) {
		end = len(items)
	}
	paginatedItems := items[start:end]

	result := &DailyReportResult{
		Items:      paginatedItems,
		TotalCount: totalCount,
		Page:       page,
		PageSize:   pageSize,
		StartDate:  startDate,
		EndDate:    endDate,
	}

	g.Log().Debugf(ctx, "Daily report for tenant %d: found %d days of data",
		tenantId, totalCount)

	return result, nil
}

// FormatReportTable formats the report data as a pipe-separated table
func (_ *sDailyReport) FormatReportTable(report *DailyReportResult) string {
	if report == nil || len(report.Items) == 0 {
		return "暂无数据"
	}

	// Build header
	var lines []string
	lines = append(lines, "日期 | 存款 | 取款")
	lines = append(lines, "---|---|---")

	// Build data rows
	for _, item := range report.Items {
		line := fmt.Sprintf("%s | %s | %s",
			item.Date,
			item.DepositAmount.String(),
			item.WithdrawalAmount.String(),
		)
		lines = append(lines, line)
	}

	return strings.Join(lines, "\n")
}
