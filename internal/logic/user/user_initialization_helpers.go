package user

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"telegram-bot-api/internal/config"
	"telegram-bot-api/internal/consts"
	"telegram-bot-api/internal/dao"
	"telegram-bot-api/internal/middleware"
	"telegram-bot-api/internal/model"
	"telegram-bot-api/internal/model/entity"
	"telegram-bot-api/internal/service"
	"time"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"
)

// _initializeNewUser 在事务中创建新用户及其关联资源（钱包等）
// 破坏性更新：强制要求租户上下文
func (s *sUser) _initializeNewUser(ctx context.Context, tx gdb.TX, tgUser *tgbotapi.User, chatID int64, agentInviteCode string, gameUsername string) (*entity.Users, error) {
	// 强制要求租户上下文 - 多租户架构核心原则
	tenantId, ok := middleware.GetTenantIdFromContext(ctx)
	if !ok {
		return nil, gerror.New("TENANT_REQUIRED: 用户创建必须在租户上下文中进行")
	}

	inviteCode, err := s.generateInviteCode(ctx)
	if err != nil {
		return nil, err
	}

	// 初始化用于存储解析结果的变量
	var parsedAgentRelationship string
	var parsedFirstId uint
	var parsedSecondId uint
	var parsedThirdId uint
	var parsedRecommendId uint64
	var parsedRecommendRelationship string
	var parsedDeep int

	// 处理邀请码 (如果 agentInviteCode 不为空)
	if agentInviteCode != "" {
		// 首先尝试在 users 表中查找（用户邀请）
		var inviter *entity.Users
		userErr := dao.Users.Ctx(ctx).TX(tx).Where(dao.Users.Columns().InviteCode, agentInviteCode).Where(dao.Users.Columns().IsStop, 0).WhereNull(dao.Users.Columns().DeletedAt).Scan(&inviter)

		if userErr == nil && inviter != nil {
			// 找到了邀请用户，设置推荐关系
			parsedRecommendId = inviter.Id

			// 构建推荐关系路径
			if inviter.RecommendRelationship != "" {
				// 如果邀请人已有推荐链，则在其基础上追加
				parsedRecommendRelationship = fmt.Sprintf("%s%d/", inviter.RecommendRelationship, inviter.Id)
			} else {
				// 如果邀请人是第一级，则从其ID开始
				parsedRecommendRelationship = fmt.Sprintf("/%d/", inviter.Id)
			}

			// 计算推荐深度
			if inviter.RecommendRelationship != "" {
				relationshipStr := strings.Trim(inviter.RecommendRelationship, "/")
				if relationshipStr != "" {
					parts := strings.Split(relationshipStr, "/")
					parsedDeep = len(parts) + 1
				} else {
					parsedDeep = 1
				}
			} else {
				parsedDeep = 1
			}

			// 同时继承邀请人的代理关系（如果有）
			if inviter.AgentRelationship != "" {
				parsedAgentRelationship = inviter.AgentRelationship
				parsedFirstId = inviter.FirstId
				parsedSecondId = inviter.SecondId
				parsedThirdId = inviter.ThirdId
			}

			g.Log().Infof(ctx, "用户邀请码 %s 找到邀请人 ID: %d, 推荐关系: %s, 深度: %d", agentInviteCode, inviter.Id, parsedRecommendRelationship, parsedDeep)
		} else {
			// 如果在 users 表中没找到，再尝试在 agents 表中查找（代理邀请）
			var agent *entity.Agents
			dbErr := dao.Agents.Ctx(ctx).TX(tx).Where(dao.Agents.Columns().InvitationCode, agentInviteCode).Where(dao.Agents.Columns().Status, 1).Scan(&agent)

			// 校验代理信息并解析 relationship
			if dbErr == nil && agent != nil && agent.Relationship != "" {
				parsedAgentRelationship = agent.Relationship
				relationshipStr := strings.Trim(agent.Relationship, "/")
				parts := strings.Split(relationshipStr, "/")
				if len(parts) == 3 {
					parsedFirstId = gconv.Uint(parts[0])
					parsedSecondId = gconv.Uint(parts[1])
					parsedThirdId = gconv.Uint(parts[2])
				} else {
					g.Log().Warningf(ctx, "代理 relationship 格式不符合预期的三级结构: %s", agent.Relationship)
				}
				g.Log().Infof(ctx, "代理邀请码 %s 找到代理信息, 代理关系: %s", agentInviteCode, agent.Relationship)
			} else {
				g.Log().Warningf(ctx, "邀请码 %s 在用户表和代理表中都未找到有效记录", agentInviteCode)
			}
		}
	}

	// 3.1 创建用户记录
	// Always default to Chinese for new users, independent of Telegram language
	defaultLanguage := consts.DefaultLanguage
	g.Log().Infof(ctx, "Creating user %d with default language: %s (Telegram language %s ignored)", tgUser.ID, defaultLanguage, tgUser.LanguageCode)

	// Get default deposit reward multiplier from config
	defaultDepositRewardMultiplier := s.getDefaultDepositRewardMultiplier(ctx)

	// Note: Name and Nickname fields removed as they are unreliable
	// User display information is stored in user_backup_accounts table
	newUser := &entity.Users{
		Account:              gconv.String(tgUser.ID),
		InviteCode:           inviteCode,
		TenantId:             int(tenantId), // 关键修复：添加租户ID，确保数据隔离
		IsStop:               0,
		CreatedAt:            gtime.Now(),
		UpdatedAt:            gtime.Now(),
		Language:             defaultLanguage,
		RedPacketPermission:  1,
		TransferPermission:   1,
		WithdrawPermission:   1,
		FlashTradePermission: 1,
		RechargePermission:   1,
		ReceivePermission:    1,
		// 推荐关系字段
		RecommendId:           parsedRecommendId,
		RecommendRelationship: parsedRecommendRelationship,
		Deep:                  parsedDeep,
		// 代理相关字段
		AgentRelationship: parsedAgentRelationship,
		FirstId:           parsedFirstId,
		SecondId:          parsedSecondId,
		ThirdId:           parsedThirdId,
		// MainWalletId 稍后更新
		DepositRewardMultiplier: defaultDepositRewardMultiplier,
		WithdrawBettingVolume:   decimal.Zero, // 默认提现流水为0
		GameUsername:            gameUsername,
	}
	result, err := dao.Users.Ctx(ctx).TX(tx).Data(newUser).Insert()
	if err != nil {
		g.Log().Errorf(ctx, "TX: Failed to create new user for telegram ID %d: %v", tgUser.ID, err)
		return nil, err // Rollback
	}
	newUserID64, err := result.LastInsertId()
	if err != nil {
		g.Log().Errorf(ctx, "TX: Failed to get last insert ID for new user (telegram ID %d): %v", tgUser.ID, err)
		return nil, err // Rollback
	}
	newUserID := uint64(newUserID64)
	newUser.Id = newUserID // 设置 ID
	g.Log().Infof(ctx, "TX: Successfully inserted new user with telegram ID %d, UserID: %d", tgUser.ID, newUserID)

	// 3.2 检查并创建主备份账户记录 - 包含用户显示信息
	// Note: LastName not available in user_backup_accounts table, combining with FirstName
	fullName := tgUser.FirstName
	if tgUser.LastName != "" {
		fullName = tgUser.FirstName + " " + tgUser.LastName
	}

	_, err = dao.UserBackupAccounts.Ctx(ctx).TX(tx).Data(g.Map{
		dao.UserBackupAccounts.Columns().TelegramId:       tgUser.ID,
		dao.UserBackupAccounts.Columns().TelegramUsername: fmt.Sprintf("@%s", tgUser.UserName),
		dao.UserBackupAccounts.Columns().FirstName:        fullName,    // Combined first and last name
		dao.UserBackupAccounts.Columns().UserId:           newUserID,   // 用户ID
		dao.UserBackupAccounts.Columns().ChatId:           chatID,      // Added ChatId
		dao.UserBackupAccounts.Columns().TenantId:         tenantId,    // 关键修复：添加租户ID，确保数据隔离
		dao.UserBackupAccounts.Columns().VerifiedAt:       gtime.Now(), // 验证时间
		dao.UserBackupAccounts.Columns().CreatedAt:        gtime.Now(), // 创建时间
		dao.UserBackupAccounts.Columns().UpdatedAt:        gtime.Now(), // 更新时间
		dao.UserBackupAccounts.Columns().IsMaster:         1,           // 是否为主账户
	}).Insert()
	if err != nil {
		g.Log().Errorf(ctx, "TX: Failed to create backup account for new user %d (telegram ID %d, chatID %d): %v", newUserID, tgUser.ID, chatID, err)
		return nil, err // Rollback
	}
	g.Log().Infof(ctx, "TX: Successfully created master backup account for user %d (telegram ID %d)", newUserID, tgUser.ID)

	// 3.3 创建用户的主钱包
	// 使用新的钱包模块V2版本
	err = s.createUserWalletV2(ctx, newUser, tgUser, tx)
	if err != nil {
		g.Log().Errorf(ctx, "TX: Failed to create main wallet for user %d: %v", newUserID, err)
		return nil, err // Rollback
	}

	// 存储完整姓名到用户对象中，供后续发送通知使用
	newUser.Avatar = fullName // 临时使用Avatar字段存储fullName，用于通知

	return newUser, nil // 返回成功创建的用户对象
}

// sendUserRegistrationNotifications 发送用户注册通知给推荐人
func (s *sUser) sendUserRegistrationNotifications(_ context.Context, newUser *entity.Users, tgUser *tgbotapi.User) {
	// 创建新的context，避免原context被取消影响通知发送
	notifyCtx := context.Background()

	// 发送直接推荐人通知并更新统计
	if newUser.RecommendId > 0 {
		// 更新直接推荐人的邀请统计
		if err := s.updateInviterStatistics(notifyCtx, newUser.RecommendId, true); err != nil {
			g.Log().Errorf(notifyCtx, "Failed to update direct inviter statistics for user %d: %v", newUser.RecommendId, err)
		}

		payload := model.UserRegisteredPayload{
			RegisteredUserID:       int64(tgUser.ID),
			RegisteredUsername:     tgUser.UserName,
			RegisteredUserFullName: newUser.Avatar, // 从Avatar字段获取fullName
			IsDirectInvite:         true,
			InviteLevel:            1,
		}

		if err := s.sendRegistrationNotification(notifyCtx, newUser.RecommendId, payload, uint(newUser.TenantId)); err != nil {
			g.Log().Errorf(notifyCtx, "Failed to send direct invite notification to user %d: %v", newUser.RecommendId, err)
		}
	}

	// 解析推荐关系链，发送间接推荐通知
	if newUser.RecommendRelationship != "" && newUser.RecommendId > 0 {
		relationshipStr := strings.Trim(newUser.RecommendRelationship, "/")
		if relationshipStr != "" {
			parts := strings.Split(relationshipStr, "/")
			// 跳过最后一个ID（直接推荐人），因为已经发送过了
			for i := len(parts) - 2; i >= 0; i-- {
				indirectInviterID := gconv.Uint64(parts[i])
				if indirectInviterID > 0 {
					// 更新间接推荐人的邀请统计
					if err := s.updateInviterStatistics(notifyCtx, indirectInviterID, false); err != nil {
						g.Log().Errorf(notifyCtx, "Failed to update indirect inviter statistics for user %d: %v", indirectInviterID, err)
					}

					payload := model.UserRegisteredPayload{
						RegisteredUserID:       int64(tgUser.ID),
						RegisteredUsername:     tgUser.UserName,
						RegisteredUserFullName: newUser.Avatar, // 从Avatar字段获取fullName
						IsDirectInvite:         false,
						InviteLevel:            len(parts) - i,
					}

					if err := s.sendRegistrationNotification(notifyCtx, indirectInviterID, payload, uint(newUser.TenantId)); err != nil {
						g.Log().Errorf(notifyCtx, "Failed to send indirect invite notification to user %d: %v", indirectInviterID, err)
					}
				}
			}
		}
	}
}

// sendRegistrationNotification 发送单个注册通知
func (s *sUser) sendRegistrationNotification(ctx context.Context, inviterID uint64, payload model.UserRegisteredPayload, tenantID uint) error {
	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		return gerror.Wrap(err, "failed to marshal notification payload")
	}

	notification := model.UnifiedNotificationMessage{
		NotificationKey: consts.NotificationKeyUserRegistered,
		UserID:          int64(inviterID),
		TenantId:        tenantID,
		Timestamp:       time.Now().Unix(),
		TraceID:         gconv.String(gtime.TimestampNano()),
		Payload:         payloadJSON,
	}

	// 序列化通知消息
	msgBytes, err := json.Marshal(notification)
	if err != nil {
		return gerror.Wrap(err, "failed to marshal notification message")
	}

	// 获取Kafka topic
	topic := g.Cfg().MustGet(ctx, "kafka.topics.unifiedNotifications", "unified_notifications").String()

	// 发送到Kafka
	key := fmt.Sprintf("%s_%d_%s", notification.NotificationKey, notification.UserID, notification.TraceID)
	if err := service.Kafka().SendMessage(ctx, topic, key, msgBytes); err != nil {
		return gerror.Wrap(err, "failed to send notification to kafka")
	}

	g.Log().Infof(ctx, "User registration notification sent: UserID=%d, InviterID=%d, IsDirectInvite=%v",
		payload.RegisteredUserID, inviterID, payload.IsDirectInvite)

	return nil
}

// updateInviterStatistics 更新推荐人的邀请统计信息
func (s *sUser) updateInviterStatistics(ctx context.Context, inviterID uint64, isDirect bool) error {
	// 使用事务确保数据一致性
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 获取当前时间
		now := gtime.Now()

		// 构建更新字段
		updateData := g.Map{
			"last_invite_at": now,
		}

		// 根据是否直接邀请选择更新的字段
		if isDirect {
			updateData["direct_invites_count"] = gdb.Raw("direct_invites_count + 1")
		} else {
			updateData["indirect_invites_count"] = gdb.Raw("indirect_invites_count + 1")
		}
		updateData["total_invites_count"] = gdb.Raw("total_invites_count + 1")

		// 同时更新首次邀请时间（如果是第一次邀请）
		// 需要先查询是否已有首次邀请时间
		var user entity.Users
		err := dao.Users.Ctx(ctx).TX(tx).
			Where(dao.Users.Columns().Id, inviterID).
			Fields("first_invite_at").
			Scan(&user)
		if err == nil && user.FirstInviteAt == nil {
			updateData["first_invite_at"] = now
		}

		// 执行更新
		_, err = dao.Users.Ctx(ctx).TX(tx).
			Where(dao.Users.Columns().Id, inviterID).
			Data(updateData).
			Update()

		if err != nil {
			return gerror.Wrapf(err, "failed to update inviter statistics for user %d", inviterID)
		}

		g.Log().Infof(ctx, "Updated inviter statistics for user %d: isDirect=%v", inviterID, isDirect)
		return nil
	})
}

// getDefaultDepositRewardMultiplier reads the run_reward_map config and returns the key with the maximum value
func (s *sUser) getDefaultDepositRewardMultiplier(ctx context.Context) string {
	// Get reward map from config
	rewardMapStr, err := config.GetString(ctx, "recharges_setting.run_reward_map", "{\"0\":\"1\",\"3\":\"3\",\"5\":\"5\"}")
	if err != nil {
		g.Log().Warningf(ctx, "Failed to get run_reward_map for default deposit reward: %v, using 0", err)
		return "0"
	}

	// Parse reward map
	rewardMap := make(map[string]float64)
	if j := gjson.New(rewardMapStr); j != nil {
		if m := j.Map(); m != nil {
			for k, v := range m {
				val := ""
				if strVal, ok := v.(string); ok {
					val = strVal
				} else {
					val = fmt.Sprintf("%v", v)
				}
				// Convert value to float for comparison
				floatVal := gconv.Float64(val)
				rewardMap[k] = floatVal
			}
		}
	}

	// Find the key with maximum value
	maxKey := "0"
	maxValue := 0.0
	for k, v := range rewardMap {
		if v > maxValue {
			maxValue = v
			maxKey = k
		}
	}

	g.Log().Infof(ctx, "Default deposit reward multiplier set to: %s (flow multiplier: %.1f)", maxKey, maxValue)
	return maxKey
}
